<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/white">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/sort_by_title"
        android:textColor="@color/gray"
        android:textSize="14sp"
        android:gravity="center"
        android:padding="8dp"
        android:paddingStart="20dp"
        android:background="@android:color/white" />

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/light_gray" />

    <!-- Date Sort Option -->
    <LinearLayout
        android:id="@+id/sortByDateLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="18dp"
        android:paddingBottom="18dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_date"
            android:textColor="@color/dark_blue"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/dateSortIcon"
            android:layout_width="25dp"
            android:layout_height="24dp"
            android:src="@drawable/arrow_icon"
            android:layout_marginStart="8dp"
            android:visibility="invisible" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/light_gray" />

    <!-- Time Sort Option -->
    <LinearLayout
        android:id="@+id/sortByTimeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="18dp"
        android:paddingBottom="18dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_time"
            android:textColor="@color/dark_blue"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/timeSortIcon"
            android:layout_width="25dp"
            android:layout_height="24dp"
            android:src="@drawable/arrow_icon"
            android:layout_marginStart="8dp"
            android:visibility="invisible" />

    </LinearLayout>

    <!-- Bottom padding -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:background="@android:color/white" />

</LinearLayout>
