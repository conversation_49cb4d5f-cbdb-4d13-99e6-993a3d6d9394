<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="@drawable/selectable_background">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Main notification layout with side panel -->
        <LinearLayout
            android:id="@+id/notificationContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/white">

            <!-- Notification content (takes remaining space) -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <!-- Status Indicator -->
                <View
                    android:id="@+id/statusIndicator"
                    android:layout_width="8dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp"
                    android:background="@color/baby_pink" />

                <!-- Notification Icon -->
                <ImageView
                    android:id="@+id/notificationIcon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_notifications"
                    android:background="@drawable/circle_background"
                    android:padding="8dp"
                    app:tint="@color/baby_pink" />

                <!-- Content -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <!-- Title -->
                    <TextView
                        android:id="@+id/notificationTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Baby Care Reminder"
                        android:textColor="@color/dark_blue"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <!-- Details -->
                    <TextView
                        android:id="@+id/notificationDetails"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Scheduled for 3 hours from last activity"
                        android:textColor="@color/gray"
                        android:textSize="14sp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                    <!-- Time Info -->
                    <TextView
                        android:id="@+id/notificationTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Next reminder: 15:30"
                        android:textColor="@color/baby_pink"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Original Cancel Button (keep for click functionality) -->
                <ImageView
                    android:id="@+id/cancelButton"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_close"
                    android:background="@drawable/selectable_background"
                    android:padding="6dp"
                    app:tint="@color/gray"
                    android:contentDescription="Cancel notification" />

            </LinearLayout>

            <!-- Cancel side panel (shown on swipe) -->
            <LinearLayout
                android:id="@+id/cancelOverlay"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@android:color/holo_orange_dark"
                android:visibility="gone">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_close"
                    android:layout_margin="14dp"
                    app:tint="@color/white"
                    android:contentDescription="Cancel notification" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

</androidx.cardview.widget.CardView>
