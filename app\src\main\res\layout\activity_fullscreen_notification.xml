<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/baby_pink"
    android:padding="24dp"
    android:gravity="center">

    <!-- Notification Icon -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@android:drawable/ic_dialog_alert"
        android:tint="@color/dark_blue"
        android:background="@drawable/circle_background"
        android:padding="24dp" />

    <!-- Title -->
    <TextView
        android:id="@+id/notificationTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/reminder_title"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/dark_blue"
        android:gravity="center"
        android:textAlignment="center" />

    <!-- Current Time -->
    <TextView
        android:id="@+id/notificationTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="@string/reminder_current_time_placeholder"
        android:textSize="16sp"
        android:textColor="@color/dark_blue"
        android:gravity="center" />

    <!-- Message -->
    <TextView
        android:id="@+id/notificationMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="48dp"
        android:text="@string/reminder_1_hour"
        android:textSize="18sp"
        android:textColor="@color/dark_blue"
        android:gravity="center"
        android:textAlignment="center"
        android:lineSpacingExtra="4dp"
        android:padding="16dp"
        android:background="@drawable/rounded_background_white"
        android:elevation="4dp" />

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center">

        <!-- Add Entry Button (Primary Action) -->
        <Button
            android:id="@+id/addEntryButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="@string/reminder_add_entry"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:background="@drawable/button_primary"
            android:elevation="4dp" />

        <!-- Snooze Button -->
        <Button
            android:id="@+id/snoozeButton"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="12dp"
            android:text="@string/reminder_snooze"
            android:textSize="14sp"
            android:textColor="@color/dark_blue"
            android:background="@drawable/button_secondary"
            android:elevation="2dp" />

        <!-- Dismiss Button -->
        <Button
            android:id="@+id/dismissButton"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/reminder_dismiss"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:background="@drawable/button_tertiary"
            android:elevation="1dp" />

    </LinearLayout>

    <!-- Bottom Spacing -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:minHeight="24dp" />

</LinearLayout>
