<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <!-- Swipe Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Background with Edit/Delete buttons (revealed on swipe) -->
        <LinearLayout
            android:id="@+id/backgroundLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="end"
            android:background="@color/light_pink">

            <Button
                android:id="@+id/editButton"
                android:layout_width="80dp"
                android:layout_height="match_parent"
                android:backgroundTint="@color/baby_pink"
                android:text="@string/edit_button"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/deleteButton"
                android:layout_width="80dp"
                android:layout_height="match_parent"
                android:backgroundTint="@android:color/holo_red_dark"
                android:text="@string/delete_button"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Main content layout with side panel -->
        <LinearLayout
            android:id="@+id/foregroundLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/white">

            <!-- Entry content (takes remaining space) -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Header with time and date -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp"
                    android:gravity="center_vertical">

                    <!-- Selection Checkbox (Initially Hidden) -->
                    <CheckBox
                        android:id="@+id/selectionCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:buttonTint="@color/baby_pink"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/timeText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="14:30"
                        android:textColor="@color/dark_blue"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/dateText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="May 30, 2025"
                        android:textColor="@color/gray"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Activity summary -->
                <TextView
                    android:id="@+id/activityText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Diaper: 💩 💧 • Nursing: L(14:15)"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <!-- Description -->
                <TextView
                    android:id="@+id/descriptionText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Baby was fussy before feeding..."
                    android:textColor="@color/gray"
                    android:textSize="12sp"
                    android:maxLines="2"
                    android:ellipsize="end" />

            </LinearLayout>

            <!-- Delete side panel (shown on swipe) -->
            <LinearLayout
                android:id="@+id/deleteOverlay"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@android:color/holo_red_dark"
                android:visibility="gone">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@android:drawable/ic_menu_delete"
                    android:layout_margin="14dp"
                    app:tint="@color/white"
                    android:contentDescription="@string/delete_entry_icon" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

</androidx.cardview.widget.CardView>
