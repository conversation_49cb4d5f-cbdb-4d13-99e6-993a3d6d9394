package com.example.babyapp;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Activity for configuring nursing notification time intervals
 */
public class NotificationSettingsActivity extends BaseActivity {

    // UI Components
    private ImageView backButton;
    private RadioGroup timeIntervalRadioGroup;
    private RadioButton radio1Hour, radio2Hours, radio3Hours, radio4Hours, radio6Hours, radioCustom;
    private LinearLayout customTimeLayout;
    private EditText customHoursEditText, customMinutesEditText;
    private TextView previewText;
    private Switch vibrationSwitch;
    private Switch soundSwitch;
    private Button cancelButton, saveButton;

    // Current settings
    private int currentIntervalMinutes = 180; // Default 3 hours
    private boolean vibrationEnabled = true; // Default vibration enabled
    private boolean soundEnabled = true; // Default sound enabled

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notification_settings);

        // Get current settings from intent
        Intent intent = getIntent();
        if (intent.hasExtra("current_interval_minutes")) {
            currentIntervalMinutes = intent.getIntExtra("current_interval_minutes", 180);
        }
        if (intent.hasExtra("vibration_enabled")) {
            vibrationEnabled = intent.getBooleanExtra("vibration_enabled", true);
        }
        if (intent.hasExtra("sound_enabled")) {
            soundEnabled = intent.getBooleanExtra("sound_enabled", true);
        }

        initializeComponents();
        setupClickListeners();
        loadCurrentSettings();
        updatePreview();
    }

    /**
     * Initialize all UI components
     */
    private void initializeComponents() {
        backButton = findViewById(R.id.backButton);
        timeIntervalRadioGroup = findViewById(R.id.timeIntervalRadioGroup);
        radio1Hour = findViewById(R.id.radio1Hour);
        radio2Hours = findViewById(R.id.radio2Hours);
        radio3Hours = findViewById(R.id.radio3Hours);
        radio4Hours = findViewById(R.id.radio4Hours);
        radio6Hours = findViewById(R.id.radio6Hours);
        radioCustom = findViewById(R.id.radioCustom);
        customTimeLayout = findViewById(R.id.customTimeLayout);
        customHoursEditText = findViewById(R.id.customHoursEditText);
        customMinutesEditText = findViewById(R.id.customMinutesEditText);
        previewText = findViewById(R.id.previewText);
        vibrationSwitch = findViewById(R.id.vibrationSwitch);
        soundSwitch = findViewById(R.id.soundSwitch);
        cancelButton = findViewById(R.id.cancelButton);
        saveButton = findViewById(R.id.saveButton);
    }

    /**
     * Set up click listeners for all interactive elements
     */
    private void setupClickListeners() {
        // Back button
        backButton.setOnClickListener(v -> finish());

        // Radio group listener
        timeIntervalRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radioCustom) {
                customTimeLayout.setVisibility(View.VISIBLE);
            } else {
                customTimeLayout.setVisibility(View.GONE);
            }
            updatePreview();
        });

        // Custom time input listeners
        customHoursEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updatePreview();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        customMinutesEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updatePreview();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // Action buttons
        cancelButton.setOnClickListener(v -> finish());
        saveButton.setOnClickListener(v -> saveSettings());
    }

    /**
     * Load current settings and set appropriate radio button, vibration switch, and sound switch
     */
    private void loadCurrentSettings() {
        // Set vibration switch state
        if (vibrationSwitch != null) {
            vibrationSwitch.setChecked(vibrationEnabled);
        }

        // Set sound switch state
        if (soundSwitch != null) {
            soundSwitch.setChecked(soundEnabled);
        }

        // Set interval radio button
        switch (currentIntervalMinutes) {
            case 60:
                radio1Hour.setChecked(true);
                break;
            case 120:
                radio2Hours.setChecked(true);
                break;
            case 180:
                radio3Hours.setChecked(true);
                break;
            case 240:
                radio4Hours.setChecked(true);
                break;
            case 360:
                radio6Hours.setChecked(true);
                break;
            default:
                radioCustom.setChecked(true);
                customTimeLayout.setVisibility(View.VISIBLE);
                int hours = currentIntervalMinutes / 60;
                int minutes = currentIntervalMinutes % 60;
                customHoursEditText.setText(String.valueOf(hours));
                customMinutesEditText.setText(String.valueOf(minutes));
                break;
        }
    }

    /**
     * Update the preview text based on current selection
     */
    private void updatePreview() {
        int intervalMinutes = getCurrentIntervalMinutes();
        String previewMessage;

        if (intervalMinutes <= 0) {
            previewMessage = getString(R.string.preview_invalid_time);
        } else if (intervalMinutes < 60) {
            previewMessage = getString(R.string.preview_minutes, intervalMinutes);
        } else {
            int hours = intervalMinutes / 60;
            int minutes = intervalMinutes % 60;

            if (minutes == 0) {
                if (hours == 1) {
                    previewMessage = getString(R.string.preview_1_hour);
                } else {
                    previewMessage = getString(R.string.preview_hours, hours);
                }
            } else {
                if (hours == 1) {
                    previewMessage = getString(R.string.preview_1_hour_minutes, minutes);
                } else {
                    previewMessage = getString(R.string.preview_hours_minutes, hours, minutes);
                }
            }
        }

        previewText.setText(previewMessage);
    }

    /**
     * Get current interval in minutes based on selection
     */
    private int getCurrentIntervalMinutes() {
        int checkedId = timeIntervalRadioGroup.getCheckedRadioButtonId();

        if (checkedId == R.id.radio1Hour) {
            return 60;
        } else if (checkedId == R.id.radio2Hours) {
            return 120;
        } else if (checkedId == R.id.radio3Hours) {
            return 180;
        } else if (checkedId == R.id.radio4Hours) {
            return 240;
        } else if (checkedId == R.id.radio6Hours) {
            return 360;
        } else if (checkedId == R.id.radioCustom) {
            try {
                String hoursStr = customHoursEditText.getText().toString().trim();
                String minutesStr = customMinutesEditText.getText().toString().trim();

                int hours = hoursStr.isEmpty() ? 0 : Integer.parseInt(hoursStr);
                int minutes = minutesStr.isEmpty() ? 0 : Integer.parseInt(minutesStr);

                return hours * 60 + minutes;
            } catch (NumberFormatException e) {
                return 0;
            }
        }

        return 180; // Default 3 hours
    }

    /**
     * Save settings and return to previous activity
     */
    private void saveSettings() {
        int intervalMinutes = getCurrentIntervalMinutes();

        if (intervalMinutes <= 0) {
            Toast.makeText(this, getString(R.string.toast_invalid_interval), Toast.LENGTH_SHORT).show();
            return;
        }

        if (intervalMinutes > 1440) { // More than 24 hours
            Toast.makeText(this, getString(R.string.toast_interval_too_long), Toast.LENGTH_SHORT).show();
            return;
        }

        // Get vibration setting from switch
        boolean vibrationEnabled = vibrationSwitch != null ? vibrationSwitch.isChecked() : true;

        // Get sound setting from switch
        boolean soundEnabled = soundSwitch != null ? soundSwitch.isChecked() : true;

        // Return result to calling activity
        Intent resultIntent = new Intent();
        resultIntent.putExtra("interval_minutes", intervalMinutes);
        resultIntent.putExtra("vibration_enabled", vibrationEnabled);
        resultIntent.putExtra("sound_enabled", soundEnabled);
        setResult(RESULT_OK, resultIntent);

        Toast.makeText(this, getString(R.string.toast_settings_saved), Toast.LENGTH_SHORT).show();
        finish();
    }
}
