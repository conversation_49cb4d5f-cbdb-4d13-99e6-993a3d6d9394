package com.example.babyapp;

import android.Manifest;
import android.app.AlertDialog;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

/**
 * Activity for requesting critical permissions on first app launch
 * Ensures users can receive baby care notifications
 */
public class PermissionRequestActivity extends AppCompatActivity {

    private static final String TAG = "PermissionRequestActivity";
    private static final String PREFS_NAME = "BabyAppPermissions";
    private static final String KEY_PERMISSIONS_REQUESTED = "permissions_requested";
    private static final int REQUEST_NOTIFICATION_PERMISSION = 1001;
    private static final int REQUEST_EXACT_ALARM_PERMISSION = 1002;
    private static final int REQUEST_BATTERY_OPTIMIZATION = 1003;

    private TextView titleText;
    private TextView descriptionText;
    private Button confirmButton;
    private Button denyButton;
    private SharedPreferences prefs;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Hide action bar for clean popup appearance
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        setContentView(R.layout.activity_permission_request);

        prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        initializeComponents();
        setupClickListeners();
        updatePermissionDisplay();

        Log.d(TAG, "Permission request activity started");
    }

    /**
     * Initialize UI components
     */
    private void initializeComponents() {
        titleText = findViewById(R.id.permissionTitleText);
        descriptionText = findViewById(R.id.permissionDescriptionText);
        confirmButton = findViewById(R.id.confirmPermissionButton);
        denyButton = findViewById(R.id.denyPermissionButton);
    }

    /**
     * Set up click listeners for buttons
     */
    private void setupClickListeners() {
        confirmButton.setOnClickListener(v -> requestPermissions());
        denyButton.setOnClickListener(v -> denyPermissions());
    }

    /**
     * Update the display based on current permission status
     */
    private void updatePermissionDisplay() {
        titleText.setText("Baby Care Notifications");
        
        String description = "To ensure you never miss important baby care reminders, this app needs permission to:\n\n" +
                "• Send notifications when you're using other apps\n" +
                "• Show alerts even when your phone is locked\n" +
                "• Wake up your device for critical baby care reminders\n" +
                "• Work reliably in the background\n\n" +
                "These permissions are essential for your baby's safety and care schedule.";
        
        descriptionText.setText(description);
        confirmButton.setText("Grant Permissions");
        denyButton.setText("Not Now");
    }

    /**
     * Request all necessary permissions
     */
    private void requestPermissions() {
        Log.d(TAG, "User confirmed permission request");
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ requires explicit notification permission
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) 
                != PackageManager.PERMISSION_GRANTED) {
                
                ActivityCompat.requestPermissions(this, 
                    new String[]{Manifest.permission.POST_NOTIFICATIONS}, 
                    REQUEST_NOTIFICATION_PERMISSION);
                return;
            }
        }
        
        // Request exact alarm permission (Android 12+)
        requestExactAlarmPermission();
    }

    /**
     * Request exact alarm permission for reliable notifications
     */
    private void requestExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            try {
                Intent intent = new Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                intent.setData(Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, REQUEST_EXACT_ALARM_PERMISSION);
                Log.d(TAG, "Requesting exact alarm permission");
            } catch (Exception e) {
                Log.e(TAG, "Failed to request exact alarm permission", e);
                requestBatteryOptimization();
            }
        } else {
            requestBatteryOptimization();
        }
    }

    /**
     * Request battery optimization exemption
     */
    private void requestBatteryOptimization() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, REQUEST_BATTERY_OPTIMIZATION);
                Log.d(TAG, "Requesting battery optimization exemption");
            } catch (Exception e) {
                Log.e(TAG, "Failed to request battery optimization exemption", e);
                completePermissionRequest();
            }
        } else {
            completePermissionRequest();
        }
    }

    /**
     * Handle permission request results
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_NOTIFICATION_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission granted");
                requestExactAlarmPermission();
            } else {
                Log.w(TAG, "Notification permission denied");
                showPermissionDeniedDialog();
            }
        }
    }

    /**
     * Handle activity results from system settings
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        switch (requestCode) {
            case REQUEST_EXACT_ALARM_PERMISSION:
                Log.d(TAG, "Exact alarm permission result received");
                requestBatteryOptimization();
                break;
                
            case REQUEST_BATTERY_OPTIMIZATION:
                Log.d(TAG, "Battery optimization result received");
                completePermissionRequest();
                break;
        }
    }

    /**
     * Show dialog when critical permissions are denied
     */
    private void showPermissionDeniedDialog() {
        new AlertDialog.Builder(this)
            .setTitle("Permissions Required")
            .setMessage("Baby care notifications require these permissions to work properly. " +
                       "You can enable them later in Settings > Apps > Baby Care Tracker > Permissions.")
            .setPositiveButton("Open Settings", (dialog, which) -> {
                try {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                } catch (Exception e) {
                    Log.e(TAG, "Failed to open app settings", e);
                }
                completePermissionRequest();
            })
            .setNegativeButton("Continue", (dialog, which) -> completePermissionRequest())
            .setCancelable(false)
            .show();
    }

    /**
     * Handle permission denial
     */
    private void denyPermissions() {
        Log.d(TAG, "User denied permission request");
        
        new AlertDialog.Builder(this)
            .setTitle("Limited Functionality")
            .setMessage("Without these permissions, you may not receive important baby care reminders. " +
                       "You can enable notifications later in the app settings.")
            .setPositiveButton("Continue Anyway", (dialog, which) -> completePermissionRequest())
            .setNegativeButton("Grant Permissions", (dialog, which) -> requestPermissions())
            .setCancelable(false)
            .show();
    }

    /**
     * Complete permission request process and proceed to main app
     */
    private void completePermissionRequest() {
        // Mark permissions as requested
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_PERMISSIONS_REQUESTED, true);
        editor.apply();
        
        Log.d(TAG, "Permission request completed, proceeding to main app");
        
        // Start main activity
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * Check if permissions have already been requested
     */
    public static boolean hasRequestedPermissions(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_PERMISSIONS_REQUESTED, false);
    }

    /**
     * Check if notification permissions are granted
     */
    public static boolean hasNotificationPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                   == PackageManager.PERMISSION_GRANTED;
        } else {
            NotificationManager notificationManager = 
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            return notificationManager != null && notificationManager.areNotificationsEnabled();
        }
    }

    @Override
    public void onBackPressed() {
        // Prevent back button during permission request
        denyPermissions();
    }
}
