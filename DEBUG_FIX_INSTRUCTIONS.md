# Android Studio Debugger Port Error Fix

## Error: Unable to open debugger port (localhost:55262): java.net.SocketException "Connection reset"

### Solution 1: Restart ADB (Android Debug Bridge)
1. Open Terminal/Command Prompt in Android Studio (View → Tool Windows → Terminal)
2. Run these commands:
```bash
adb kill-server
adb start-server
```

### Solution 2: Clean and Rebuild Project
1. In Android Studio: Build → Clean Project
2. Wait for completion
3. Build → Rebuild Project

### Solution 3: Restart Android Studio
1. Close Android Studio completely
2. Reopen Android Studio
3. Open your project
4. Try running the app again

### Solution 4: Check USB Debugging (if using physical device)
1. On your Android device: Settings → Developer Options
2. Turn OFF USB Debugging
3. Turn ON USB Debugging again
4. Accept the computer authorization prompt

### Solution 5: Reset Android Studio Configuration
1. Close Android Studio
2. Delete these folders (if they exist):
   - Windows: `%USERPROFILE%\.android\avd\.temp`
   - Windows: `%USERPROFILE%\.AndroidStudio*\system\tmp`
3. Restart Android Studio

### Solution 6: Change Debug Port (Advanced)
1. Go to Run → Edit Configurations
2. Select your app configuration
3. In Debugger tab, change "Debug port" to a different number (e.g., 55263)
4. Apply and OK

### Solution 7: Use Different Run Configuration
1. Instead of Debug mode, try:
   - Run → Run 'app' (without debugging)
   - This will run the app without attaching the debugger

### Solution 8: Emulator-Specific Fix (if using emulator)
1. Close the emulator
2. In AVD Manager, wipe data for your virtual device
3. Start the emulator again
4. Try running the app

### Solution 9: Network/Firewall Check
1. Check if antivirus/firewall is blocking the port
2. Temporarily disable antivirus/firewall
3. Try running the app again
4. Re-enable security software after testing

### Solution 10: Ultimate Reset
1. Close Android Studio
2. Disconnect all Android devices
3. Run: `adb kill-server`
4. Restart computer
5. Open Android Studio
6. Connect device/start emulator
7. Try running the app

## Quick Test Commands
```bash
# Check ADB status
adb devices

# Check if port is in use (Windows)
netstat -an | findstr :55262

# Check if port is in use (Mac/Linux)
lsof -i :55262
```

## Alternative: Run Without Debugging
If debugging is not immediately needed:
1. Use Run button (▶️) instead of Debug button (🐛)
2. This bypasses the debugger port issue
3. App will run normally without debug capabilities
