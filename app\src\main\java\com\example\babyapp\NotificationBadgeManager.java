package com.example.babyapp;

import android.app.NotificationManager;
import android.content.Context;
import android.util.Log;

/**
 * Manages notification badges (dots) on the app icon
 * Handles clearing badges when user opens app from notification
 */
public class NotificationBadgeManager {

    private static final String TAG = "NotificationBadgeManager";

    private Context context;
    private NotificationManager notificationManager;

    public NotificationBadgeManager(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null in NotificationBadgeManager constructor");
            throw new IllegalArgumentException("Context cannot be null");
        }

        try {
            this.context = context.getApplicationContext(); // Use application context to avoid memory leaks
            this.notificationManager = (NotificationManager) this.context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (this.notificationManager == null) {
                Log.e(TAG, "NotificationManager is null - badge management will not work");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error initializing NotificationBadgeManager", e);
            throw e;
        }
    }

    /**
     * Clear all notification badges from the app icon
     * This removes the notification dot that appears on the app icon
     */
    public void clearNotificationBadges() {
        try {
            if (notificationManager != null) {
                // Cancel all notifications to clear badges
                notificationManager.cancelAll();
                Log.d(TAG, "All notification badges cleared successfully");
            } else {
                Log.e(TAG, "NotificationManager is null, cannot clear badges");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing notification badges", e);
        }
    }

    /**
     * Clear specific notification badge by ID
     * @param notificationId The ID of the notification to clear
     */
    public void clearNotificationBadge(int notificationId) {
        try {
            if (notificationManager != null) {
                notificationManager.cancel(notificationId);
                Log.d(TAG, "Notification badge cleared for ID: " + notificationId);
            } else {
                Log.e(TAG, "NotificationManager is null, cannot clear badge for ID: " + notificationId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing notification badge for ID: " + notificationId, e);
        }
    }

    /**
     * Clear badges for baby care notifications specifically
     * Clears common notification IDs used by the baby care app
     */
    public void clearBabyCareNotificationBadges() {
        try {
            if (notificationManager != null) {
                // Clear common baby care notification IDs
                notificationManager.cancel(1001); // NotificationService ID
                notificationManager.cancel(1002); // Direct notification ID
                notificationManager.cancel(2001); // Background notification ID

                Log.d(TAG, "Baby care notification badges cleared");
            } else {
                Log.e(TAG, "NotificationManager is null, cannot clear baby care badges");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing baby care notification badges", e);
        }
    }

    /**
     * Check if there are any active notifications
     * @return true if there are active notifications, false otherwise
     */
    public boolean hasActiveNotifications() {
        try {
            if (notificationManager != null && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                return notificationManager.getActiveNotifications().length > 0;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking active notifications", e);
        }
        return false;
    }

    /**
     * Get count of active notifications
     * @return number of active notifications
     */
    public int getActiveNotificationCount() {
        try {
            if (notificationManager != null && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                return notificationManager.getActiveNotifications().length;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting active notification count", e);
        }
        return 0;
    }
}
