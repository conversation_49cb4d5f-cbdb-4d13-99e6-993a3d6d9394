package com.example.babyapp;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.renderer.CellRenderer;
import com.itextpdf.layout.renderer.DrawContext;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * PDF Generator for Baby Care Tracking Tables
 * Uses iText7 library to create printable PDF tables with customizable columns and rows
 */
public class PDFGenerator {

    private static final String TAG = "PDFGenerator";

    /**
     * Helper method to check if column is Time column (supports both English and Serbian)
     */
    private static boolean isTimeColumn(String columnName) {
        return "Time".equals(columnName) || "Vreme".equals(columnName);
    }

    /**
     * Helper method to check if column is Notes column (supports both English and Serbian)
     */
    private static boolean isNotesColumn(String columnName) {
        return "Notes".equals(columnName) || "Beleške".equals(columnName) ||
               columnName.toLowerCase().contains("note") || columnName.toLowerCase().contains("beleške");
    }

    /**
     * Generate a baby care tracking table PDF
     * 
     * @param context Application context
     * @param selectedColumns List of column names to include
     * @param numberOfRows Number of empty rows to create
     * @param isLandscape Whether to use landscape orientation
     * @param includeCheckboxes Whether to include checkbox symbols in cells
     * @return File path of generated PDF, or null if failed
     */
    public static String generateBabyCareTable(Context context, List<String> selectedColumns, 
                                             int numberOfRows, boolean isLandscape, boolean includeCheckboxes) {
        try {
            Log.d(TAG, "Starting PDF generation - Columns: " + selectedColumns.size() + 
                      ", Rows: " + numberOfRows + ", Landscape: " + isLandscape + 
                      ", Checkboxes: " + includeCheckboxes);

            // Create output file
            File outputFile = createOutputFile(context);
            if (outputFile == null) {
                Log.e(TAG, "Failed to create output file");
                return null;
            }

            // Initialize PDF document
            PdfWriter writer = new PdfWriter(new FileOutputStream(outputFile));
            PdfDocument pdfDoc = new PdfDocument(writer);

            // Set page size based on orientation
            PageSize pageSize = isLandscape ? PageSize.A4.rotate() : PageSize.A4;
            Document document = new Document(pdfDoc, pageSize);

            // Add title
            addTitle(document);

            // Create optimized table using new Smart Rewrite logic
            Table table = createOptimizedTable(selectedColumns, numberOfRows, isLandscape, includeCheckboxes);
            document.add(table);

            // Close document
            document.close();

            // Handle file transfer for Android 10+
            String finalPath = handleFileTransfer(context, outputFile);

            Log.d(TAG, "PDF generated successfully: " + finalPath);
            return finalPath;

        } catch (Exception e) {
            Log.e(TAG, "Error generating PDF", e);
            return null;
        }
    }

    /**
     * Create output file in Downloads directory
     */
    private static File createOutputFile(Context context) {
        try {
            // Create filename with timestamp
            String timestamp = String.valueOf(System.currentTimeMillis());
            String filename = "baby_care_table_" + timestamp + ".pdf";

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ - Use MediaStore API
                return createOutputFileMediaStore(context, filename);
            } else {
                // Android 9 and below - Use direct file access
                return createOutputFileLegacy(filename);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error creating output file", e);
            return null;
        }
    }

    /**
     * Create output file using MediaStore API (Android 10+)
     */
    private static File createOutputFileMediaStore(Context context, String filename) {
        try {
            ContentResolver resolver = context.getContentResolver();
            ContentValues contentValues = new ContentValues();
            contentValues.put(MediaStore.Downloads.DISPLAY_NAME, filename);
            contentValues.put(MediaStore.Downloads.MIME_TYPE, "application/pdf");
            contentValues.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS);

            Uri uri = resolver.insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues);
            if (uri == null) {
                Log.e(TAG, "Failed to create MediaStore entry");
                return null;
            }

            // Create temporary file for iText7 (it needs File object)
            File tempFile = new File(context.getCacheDir(), filename);
            Log.d(TAG, "Temp file for MediaStore: " + tempFile.getAbsolutePath());
            Log.d(TAG, "Final location will be: Downloads/" + filename);

            return tempFile;

        } catch (Exception e) {
            Log.e(TAG, "Error creating MediaStore file", e);
            return null;
        }
    }

    /**
     * Create output file using legacy method (Android 9 and below)
     */
    private static File createOutputFileLegacy(String filename) {
        try {
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            if (!downloadsDir.exists()) {
                downloadsDir.mkdirs();
            }

            File outputFile = new File(downloadsDir, filename);
            Log.d(TAG, "Legacy output file: " + outputFile.getAbsolutePath());
            return outputFile;

        } catch (Exception e) {
            Log.e(TAG, "Error creating legacy file", e);
            return null;
        }
    }

    /**
     * Handle file transfer to final location
     */
    private static String handleFileTransfer(Context context, File tempFile) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ - Copy from temp file to MediaStore
                return transferToMediaStore(context, tempFile);
            } else {
                // Android 9 and below - File is already in correct location
                return tempFile.getAbsolutePath();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error transferring file", e);
            return tempFile.getAbsolutePath(); // Return temp path as fallback
        }
    }

    /**
     * Transfer temp file to MediaStore Downloads (Android 10+)
     */
    private static String transferToMediaStore(Context context, File tempFile) {
        try {
            String filename = tempFile.getName();
            ContentResolver resolver = context.getContentResolver();

            // Create MediaStore entry
            ContentValues contentValues = new ContentValues();
            contentValues.put(MediaStore.Downloads.DISPLAY_NAME, filename);
            contentValues.put(MediaStore.Downloads.MIME_TYPE, "application/pdf");
            contentValues.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS);

            Uri uri = resolver.insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues);
            if (uri == null) {
                Log.e(TAG, "Failed to create MediaStore entry for transfer");
                return tempFile.getAbsolutePath();
            }

            // Copy file content to MediaStore
            try (java.io.InputStream inputStream = new java.io.FileInputStream(tempFile);
                 java.io.OutputStream outputStream = resolver.openOutputStream(uri)) {

                if (outputStream == null) {
                    Log.e(TAG, "Failed to open MediaStore output stream");
                    return tempFile.getAbsolutePath();
                }

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            // Delete temp file
            tempFile.delete();

            String finalPath = "Downloads/" + filename;
            Log.d(TAG, "File transferred to MediaStore: " + finalPath);
            return finalPath;

        } catch (Exception e) {
            Log.e(TAG, "Error transferring to MediaStore", e);
            return tempFile.getAbsolutePath();
        }
    }

    /**
     * Add title to the document
     */
    private static void addTitle(Document document) {
        Paragraph title = new Paragraph("BABY CARE TRACKING TABLE")
                .setTextAlignment(TextAlignment.CENTER)
                .setFontSize(16)
                .setBold()
                .setMarginBottom(20);
        document.add(title);
    }

    /**
     * Create table with specified columns and rows
     */
    private static Table createTable(List<String> selectedColumns, int numberOfRows, boolean includeCheckboxes) {
        // Calculate column widths
        float[] columnWidths = calculateColumnWidths(selectedColumns);
        
        // Create table
        Table table = new Table(UnitValue.createPercentArray(columnWidths));
        table.setWidth(UnitValue.createPercentValue(100));

        // Add header row
        addHeaderRow(table, selectedColumns);

        // Add data rows
        addDataRows(table, selectedColumns, numberOfRows, includeCheckboxes);

        return table;
    }

    /**
     * Calculate optimal column widths based on selected columns
     */
    private static float[] calculateColumnWidths(List<String> selectedColumns) {
        int columnCount = selectedColumns.size();
        float[] widths = new float[columnCount];

        // Time column gets more space (check both English and Serbian)
        if (isTimeColumn(selectedColumns.get(0))) {
            widths[0] = 25f; // 25% for Time column
            
            // Distribute remaining 75% among other columns
            float remainingWidth = 75f;
            float equalWidth = remainingWidth / (columnCount - 1);
            
            for (int i = 1; i < columnCount; i++) {
                widths[i] = equalWidth;
            }
        } else {
            // Equal distribution if Time is not first
            float equalWidth = 100f / columnCount;
            for (int i = 0; i < columnCount; i++) {
                widths[i] = equalWidth;
            }
        }

        return widths;
    }

    /**
     * Add header row with column names
     */
    private static void addHeaderRow(Table table, List<String> selectedColumns) {
        for (String columnName : selectedColumns) {
            Cell headerCell = new Cell()
                    .add(new Paragraph(columnName.toUpperCase()))
                    .setTextAlignment(TextAlignment.CENTER)
                    .setBold()
                    .setFontSize(10);
            table.addHeaderCell(headerCell);
        }
    }

    /**
     * Add empty data rows for user input
     */
    private static void addDataRows(Table table, List<String> selectedColumns, int numberOfRows, boolean includeCheckboxes) {
        for (int row = 0; row < numberOfRows; row++) {
            for (int col = 0; col < selectedColumns.size(); col++) {
                String columnName = selectedColumns.get(col);
                Cell dataCell = createDataCell(columnName, includeCheckboxes);
                table.addCell(dataCell);
            }
        }
    }

    /**
     * Create individual data cell based on column type
     */
    private static Cell createDataCell(String columnName, boolean includeCheckboxes) {
        Cell cell = new Cell();
        
        if (isTimeColumn(columnName)) {
            // Time column - empty for user to fill in
            cell.add(new Paragraph(""))
                .setHeight(25)
                .setTextAlignment(TextAlignment.CENTER);
        } else {
            // Activity columns - checkbox or empty based on user preference
            if (includeCheckboxes) {
                cell.add(new Paragraph("☐"))
                    .setHeight(25)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontSize(12);
            } else {
                cell.add(new Paragraph(""))
                    .setHeight(25)
                    .setTextAlignment(TextAlignment.CENTER);
            }
        }
        
        return cell;
    }

    /**
     * Get maximum number of rows that fit on page based on orientation
     * Updated to use Smart Rewrite calculations
     */
    public static int getMaxRows(boolean isLandscape) {
        // Use optimized layout calculator for accurate row count
        PDFLayoutCalculator layout = new PDFLayoutCalculator(isLandscape, 6, 1); // Assume max 6 columns for calculation
        float availableHeight = layout.getAvailableTableHeight();
        float minRowHeight = 15f; // Minimum readable row height

        int maxRows = (int) Math.floor(availableHeight / minRowHeight);

        // Apply reasonable limits
        int calculatedMax = isLandscape ? Math.min(maxRows, 25) : Math.min(maxRows, 35);

        Log.d(TAG, "Calculated max rows for " + (isLandscape ? "landscape" : "portrait") + ": " + calculatedMax);
        return calculatedMax;
    }

    // ========================================
    // OPTIMIZED METHODS - Smart Rewrite
    // ========================================

    /**
     * Create optimized table using Smart Rewrite logic
     */
    private static Table createOptimizedTable(List<String> selectedColumns, int numberOfRows,
                                            boolean isLandscape, boolean includeCheckboxes) {

        Log.d(TAG, "=== PDF GENERATION DEBUG START ===");
        Log.d(TAG, "Creating optimized table - Columns: " + selectedColumns.size() +
                  ", Rows: " + numberOfRows + ", Landscape: " + isLandscape +
                  ", Checkboxes: " + includeCheckboxes);
        Log.d(TAG, "Selected columns: " + selectedColumns);

        // Initialize layout calculator
        PDFLayoutCalculator layout = new PDFLayoutCalculator(isLandscape, selectedColumns.size(), numberOfRows);

        // This will trigger detailed debug logs in the calculator methods
        float availableWidth = layout.getAvailableWidth();
        float availableTableHeight = layout.getAvailableTableHeight();
        float optimalRowHeight = layout.getOptimalRowHeight();
        float[] columnWidths = layout.getOptimalColumnWidths(selectedColumns);

        Log.d(TAG, "=== SUMMARY ===");
        Log.d(TAG, "Available width: " + availableWidth + "pt");
        Log.d(TAG, "Available table height: " + availableTableHeight + "pt");
        Log.d(TAG, "Optimal row height: " + optimalRowHeight + "pt");
        Log.d(TAG, "=== PDF GENERATION DEBUG END ===");

        // Create optimized table
        OptimizedTableBuilder tableBuilder = new OptimizedTableBuilder(layout, selectedColumns, numberOfRows, includeCheckboxes);
        return tableBuilder.createOptimizedTable();
    }

    /**
     * PDF Layout Calculator - calculates optimal dimensions
     */
    private static class PDFLayoutCalculator {
        private final boolean isLandscape;
        private final int numberOfColumns;
        private final int numberOfRows;

        // Page dimensions in points (1 inch = 72 points)
        private final float pageWidth;
        private final float pageHeight;
        private final float marginTop = 40f;    // ~14mm
        private final float marginBottom = 40f; // ~14mm
        private final float marginLeft = 30f;   // ~10mm
        private final float marginRight = 30f;  // ~10mm

        public PDFLayoutCalculator(boolean isLandscape, int numberOfColumns, int numberOfRows) {
            this.isLandscape = isLandscape;
            this.numberOfColumns = numberOfColumns;
            this.numberOfRows = numberOfRows;

            if (isLandscape) {
                this.pageWidth = 842f;  // A4 landscape width
                this.pageHeight = 595f; // A4 landscape height
            } else {
                this.pageWidth = 595f;  // A4 portrait width
                this.pageHeight = 842f; // A4 portrait height
            }
        }

        public float getAvailableWidth() {
            return pageWidth - marginLeft - marginRight;
        }

        public float getAvailableHeight() {
            return pageHeight - marginTop - marginBottom;
        }

        public float getTitleHeight() {
            return 30f; // Title + spacing
        }

        public float getHeaderHeight() {
            return 25f; // Column headers
        }

        public float getAvailableTableHeight() {
            return getAvailableHeight() - getTitleHeight() - getHeaderHeight();
        }

        public float getOptimalRowHeight() {
            // Reduce available space by 35% to account for iText7 internal spacing/padding
            float availableForRows = getAvailableTableHeight() * 0.65f;
            float calculatedHeight = availableForRows / numberOfRows;

            // Ensure minimum readable height
            float minHeight = 15f;
            float maxHeight = 30f;

            float finalHeight = Math.max(minHeight, Math.min(maxHeight, calculatedHeight));

            // Debug logging for row height calculation
            Log.d(TAG, "=== ROW HEIGHT CALCULATION DEBUG ===");
            Log.d(TAG, "Page dimensions: " + pageWidth + "×" + pageHeight + "pt");
            Log.d(TAG, "Margins: top=" + marginTop + ", bottom=" + marginBottom + ", left=" + marginLeft + ", right=" + marginRight);
            Log.d(TAG, "Available page height: " + getAvailableHeight() + "pt");
            Log.d(TAG, "Title height: " + getTitleHeight() + "pt");
            Log.d(TAG, "Header height: " + getHeaderHeight() + "pt");
            Log.d(TAG, "Available for table: " + getAvailableTableHeight() + "pt");
            Log.d(TAG, "Available for rows: " + availableForRows + "pt");
            Log.d(TAG, "Number of rows: " + numberOfRows);
            Log.d(TAG, "Calculated row height: " + calculatedHeight + "pt");
            Log.d(TAG, "Min/Max limits: " + minHeight + "-" + maxHeight + "pt");
            Log.d(TAG, "Final row height: " + finalHeight + "pt");
            Log.d(TAG, "Total rows height: " + (finalHeight * numberOfRows) + "pt");
            Log.d(TAG, "Will fit on page: " + ((finalHeight * numberOfRows) <= availableForRows ? "YES" : "NO"));

            return finalHeight;
        }

        public float[] getOptimalColumnWidths(List<String> columns) {
            float[] widths = new float[columns.size()];

            Log.d(TAG, "=== COLUMN WIDTH CALCULATION DEBUG ===");
            Log.d(TAG, "Columns: " + columns);
            Log.d(TAG, "Orientation: " + (isLandscape ? "Landscape" : "Portrait"));

            if (isLandscape) {
                // Landscape optimization
                for (int i = 0; i < columns.size(); i++) {
                    String column = columns.get(i);
                    if (isTimeColumn(column)) {
                        widths[i] = 15f; // 15% for Time
                        Log.d(TAG, "Time column [" + i + "]: " + widths[i] + "%");
                    } else if (isNotesColumn(column)) {
                        widths[i] = 25f; // 25% for Notes
                        Log.d(TAG, "Notes column [" + i + "]: " + widths[i] + "%");
                    } else {
                        // Activity columns get equal share of remaining space
                        int activityColumns = columns.size() - (hasTimeColumn(columns) ? 1 : 0) - (hasNotesColumn(columns) ? 1 : 0);
                        if (activityColumns > 0) {
                            widths[i] = 60f / activityColumns; // Share 60%
                            Log.d(TAG, "Activity column [" + i + "] '" + column + "': " + widths[i] + "%");
                        } else {
                            widths[i] = 100f / columns.size(); // Fallback equal distribution
                            Log.d(TAG, "Fallback column [" + i + "] '" + column + "': " + widths[i] + "%");
                        }
                    }
                }
            } else {
                // Portrait optimization - equal distribution with slight Time preference
                if (hasTimeColumn(columns)) {
                    widths[0] = 20f; // 20% for Time
                    float remaining = 80f;
                    for (int i = 1; i < columns.size(); i++) {
                        widths[i] = remaining / (columns.size() - 1);
                    }
                    Log.d(TAG, "Portrait - Time: " + widths[0] + "%, Others: " + widths[1] + "%");
                } else {
                    // Equal distribution
                    float equalWidth = 100f / columns.size();
                    for (int i = 0; i < columns.size(); i++) {
                        widths[i] = equalWidth;
                    }
                    Log.d(TAG, "Portrait - Equal distribution: " + equalWidth + "%");
                }
            }

            // Calculate total and log final widths
            float total = 0;
            for (int i = 0; i < widths.length; i++) {
                total += widths[i];
                Log.d(TAG, "Final width[" + i + "] '" + columns.get(i) + "': " + widths[i] + "% (" + (widths[i] * getAvailableWidth() / 100f) + "pt)");
            }
            Log.d(TAG, "Total width percentage: " + total + "%");
            Log.d(TAG, "Available page width: " + getAvailableWidth() + "pt");

            return widths;
        }

        private boolean hasTimeColumn(List<String> columns) {
            return !columns.isEmpty() && isTimeColumn(columns.get(0));
        }

        private boolean hasNotesColumn(List<String> columns) {
            return columns.stream().anyMatch(col -> isNotesColumn(col));
        }
    }

    /**
     * Optimized Table Builder - creates professional tables
     */
    private static class OptimizedTableBuilder {
        private final PDFLayoutCalculator layout;
        private final List<String> columns;
        private final int numberOfRows;
        private final boolean includeCheckboxes;

        public OptimizedTableBuilder(PDFLayoutCalculator layout, List<String> columns,
                                   int numberOfRows, boolean includeCheckboxes) {
            this.layout = layout;
            this.columns = columns;
            this.numberOfRows = numberOfRows;
            this.includeCheckboxes = includeCheckboxes;
        }

        public Table createOptimizedTable() {
            // Calculate optimal column widths
            float[] columnWidths = layout.getOptimalColumnWidths(columns);

            // Create table with calculated widths
            Table table = new Table(UnitValue.createPercentArray(columnWidths));
            table.setWidth(UnitValue.createPercentValue(100));
            table.setMarginTop(10f);

            // Add header row
            addOptimizedHeaderRow(table);

            // Add data rows with optimal height
            addOptimizedDataRows(table);

            return table;
        }

        private void addOptimizedHeaderRow(Table table) {
            float headerHeight = layout.getHeaderHeight();

            for (String columnName : columns) {
                Cell headerCell = new Cell()
                        .add(new Paragraph(columnName.toUpperCase()))
                        .setTextAlignment(TextAlignment.CENTER)
                        .setBold()
                        .setFontSize(10)
                        .setHeight(headerHeight)
                        .setPadding(3f);
                table.addHeaderCell(headerCell);
            }
        }

        private void addOptimizedDataRows(Table table) {
            float rowHeight = layout.getOptimalRowHeight();

            for (int row = 0; row < numberOfRows; row++) {
                for (int col = 0; col < columns.size(); col++) {
                    String columnName = columns.get(col);
                    Cell dataCell = createOptimizedDataCell(columnName, rowHeight);
                    table.addCell(dataCell);
                }
            }
        }

        private Cell createOptimizedDataCell(String columnName, float rowHeight) {
            Cell cell = new Cell();
            cell.setHeight(rowHeight);
            cell.setPadding(2f);
            cell.setTextAlignment(TextAlignment.CENTER);

            if (isTimeColumn(columnName)) {
                // Time column - empty for user input
                cell.add(new Paragraph(""))
                    .setFontSize(9);
            } else if (isNotesColumn(columnName)) {
                // Notes column - empty with left alignment for writing
                cell.add(new Paragraph(""))
                    .setTextAlignment(TextAlignment.LEFT)
                    .setFontSize(9);
            } else {
                // Activity columns - checkbox or empty based on preference
                if (includeCheckboxes) {
                    Log.d(TAG, "Adding visual checkbox to column: " + columnName);
                    // Create empty cell and add custom checkbox renderer
                    cell.add(new Paragraph(""))
                        .setTextAlignment(TextAlignment.CENTER);

                    // Apply custom renderer to draw checkbox square
                    cell.setNextRenderer(new CheckboxCellRenderer(cell));
                    Log.d(TAG, "Applied CheckboxCellRenderer to column: " + columnName);
                } else {
                    Log.d(TAG, "Adding empty cell to column: " + columnName);
                    cell.add(new Paragraph(""))
                        .setFontSize(9);
                }
            }

            return cell;
        }
    }

    /**
     * Custom Cell Renderer for drawing checkbox squares
     */
    private static class CheckboxCellRenderer extends CellRenderer {

        public CheckboxCellRenderer(Cell modelElement) {
            super(modelElement);
        }

        @Override
        public void draw(DrawContext drawContext) {
            super.draw(drawContext);

            // Get cell position and size
            Rectangle rect = getOccupiedAreaBBox();
            PdfCanvas canvas = drawContext.getCanvas();

            // Calculate checkbox position (centered in cell)
            float checkboxSize = 10f; // 10pt checkbox
            float x = rect.getX() + (rect.getWidth() - checkboxSize) / 2;
            float y = rect.getY() + (rect.getHeight() - checkboxSize) / 2;

            // Draw checkbox square
            canvas.saveState()
                  .setLineWidth(1f)
                  .rectangle(x, y, checkboxSize, checkboxSize)
                  .stroke()
                  .restoreState();
        }
    }
}
