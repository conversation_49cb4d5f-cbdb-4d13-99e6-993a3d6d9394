<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.BabyApp" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/baby_pink</item>
        <item name="colorPrimaryContainer">@color/light_pink</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorOnPrimaryContainer">@color/dark_blue</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/light_blue</item>
        <item name="colorSecondaryContainer">@color/light_gray</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorOnSecondaryContainer">@color/dark_blue</item>
        <!-- Checkbox and radio button colors -->
        <item name="colorControlActivated">@color/baby_pink</item>
        <item name="colorAccent">@color/baby_pink</item>
        <item name="android:colorControlActivated">@color/baby_pink</item>
        <!-- Keep system status bar visible with baby pink color -->
        <item name="android:statusBarColor">@color/baby_pink</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.BabyApp" parent="Base.Theme.BabyApp" />

    <!-- Full-screen theme for notification activity -->
    <style name="Theme.BabyApp.FullScreen" parent="Base.Theme.BabyApp">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@color/baby_pink</item>
        <item name="android:statusBarColor">#00000000</item>
        <item name="android:navigationBarColor">#00000000</item>
    </style>
</resources>
