<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_blue"
    android:orientation="vertical">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/backButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/back_button"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/white" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/notifications_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Spacer for balance -->
        <View
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </LinearLayout>

    <!-- Status Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="2dp"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@string/notification_status_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/status_loading"
            android:textColor="@color/gray"
            android:textSize="14sp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/testNotificationButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/baby_pink"
                android:text="@string/test_notification_button"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/notificationSettingsButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/dark_blue"
                android:text="@string/settings_button"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Notifications List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/notificationsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="16dp"
            android:visibility="gone" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="visible">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:contentDescription="@string/no_notifications_icon"
                android:src="@drawable/ic_notifications"
                app:tint="@color/gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="@string/no_active_notifications"
                android:textColor="@color/dark_blue"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="@string/no_notifications_description"
                android:textColor="@color/gray"
                android:textSize="14sp"
                android:lineSpacingExtra="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:text="@string/notification_tip"
                android:textColor="@color/baby_pink"
                android:textSize="12sp"
                android:textStyle="italic" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="🔔"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="👶"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="🍼"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏰"
                    android:textSize="24sp" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
