package com.example.babyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

/**
 * Service for displaying nursing notifications
 */
public class NotificationService extends Service {

    private static final String TAG = "NotificationService";
    private static final String CHANNEL_ID = "nursing_notifications";
    private static final String CHANNEL_NAME = "Nursing Reminders";
    private static final int NOTIFICATION_ID = 1001;

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        try {
            Log.d(TAG, "NotificationService started");

            // Start as foreground service to ensure it runs
            try {
                startForeground(NOTIFICATION_ID, createForegroundNotification());
                Log.d(TAG, "Foreground service started successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start foreground service", e);
                // Continue without foreground service
            }

            if (intent != null) {
                String title = intent.getStringExtra("title");
                String message = intent.getStringExtra("message");
                int intervalMinutes = intent.getIntExtra("interval_minutes", 180);

                Log.d(TAG, "Processing notification: title=" + title + ", message=" + message);

                if (title != null && message != null) {
                    showNotification(title, message, intervalMinutes);
                } else {
                    Log.w(TAG, "Missing title or message in intent");
                    // Show default notification
                    showNotification("Baby Feeding Reminder", "Time to feed your baby!", intervalMinutes);
                }
            } else {
                Log.w(TAG, "Intent is null, showing default notification");
                showNotification("Baby Feeding Reminder", "Time to feed your baby!", 180);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in onStartCommand", e);
        } finally {
            // Always stop the service
            stopSelf();
        }

        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Create notification channel for Android 8.0+ with maximum importance for critical baby care
     */
    private void createNotificationChannel() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    android.app.NotificationManager.IMPORTANCE_HIGH
                );
                channel.setDescription("Critical baby care reminders - feeding, diaper, and care notifications");
                channel.enableLights(true);
                channel.setLightColor(Color.BLUE);
                channel.enableVibration(true);
                channel.setVibrationPattern(new long[]{0, 500, 200, 500}); // Custom vibration pattern
                channel.setShowBadge(true);
                channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
                channel.setBypassDnd(true); // Bypass Do Not Disturb for critical baby care
                channel.setSound(android.provider.Settings.System.DEFAULT_NOTIFICATION_URI, null);

                android.app.NotificationManager notificationManager = getSystemService(android.app.NotificationManager.class);
                if (notificationManager != null) {
                    notificationManager.createNotificationChannel(channel);
                    Log.d(TAG, "Enhanced notification channel created with maximum importance for baby care");
                } else {
                    Log.e(TAG, "NotificationManager not available for channel creation");
                }
            } else {
                Log.d(TAG, "Android version < O, no need to create notification channel");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating notification channel", e);
        }
    }

    /**
     * Create a simple foreground notification for the service
     */
    private Notification createForegroundNotification() {
        try {
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon as fallback
                .setContentTitle("Baby App")
                .setContentText("Processing notification...")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build();
        } catch (Exception e) {
            Log.e(TAG, "Error creating foreground notification", e);
            // Create minimal notification
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("Baby App")
                .setContentText("Service running")
                .build();
        }
    }

    /**
     * Show the nursing notification
     */
    private void showNotification(String title, String message, int intervalMinutes) {
        try {
            Log.d(TAG, "Showing notification: " + title + " - " + message);

            // Record that notification was sent for missed notification tracking
            try {
                MissedNotificationTracker tracker = new MissedNotificationTracker(this);
                tracker.recordNotificationSent(System.currentTimeMillis());
            } catch (Exception e) {
                Log.e(TAG, "Error recording notification for missed tracking", e);
                // Continue with notification display even if tracking fails
            }

            // Create intent to open the app when notification is tapped
            // Use flags that bring existing app to front instead of creating new instance
            Intent mainIntent = new Intent(this, MainActivity.class);
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            mainIntent.putExtra("opened_from_notification", true); // Flag to clear badge

            PendingIntent pendingIntent = null;
            try {
                pendingIntent = PendingIntent.getActivity(
                    this,
                    0,
                    mainIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
            } catch (Exception e) {
                Log.e(TAG, "Failed to create main pending intent", e);
            }

            // Create "Add Entry" action intent
            PendingIntent addEntryPendingIntent = null;
            try {
                Intent addEntryIntent = new Intent(this, AddEntryActivity.class);
                // Use flags that work with existing app state
                addEntryIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);

                addEntryPendingIntent = PendingIntent.getActivity(
                    this,
                    1,
                    addEntryIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
            } catch (Exception e) {
                Log.e(TAG, "Failed to create add entry pending intent", e);
            }

            // Build the notification with enhanced visibility and persistence
            NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon for reliability
                .setContentTitle(title != null ? title : getString(R.string.reminder_title))
                .setContentText(message != null ? message : getString(R.string.notification_message))
                .setStyle(new NotificationCompat.BigTextStyle().bigText(message != null ? message : getString(R.string.notification_message)))
                .setPriority(NotificationCompat.PRIORITY_MAX) // Maximum priority for critical baby care
                .setCategory(NotificationCompat.CATEGORY_ALARM) // Use ALARM category for better visibility
                .setAutoCancel(true)
                .setOngoing(false) // Allow user to dismiss
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // Show on lock screen
                .setLights(0xFF0000FF, 1000, 1000) // Blue light blinking
                .setDefaults(NotificationCompat.DEFAULT_ALL);

            // Add content intent if available
            if (pendingIntent != null) {
                builder.setContentIntent(pendingIntent);
            }

            // Add action if available
            if (addEntryPendingIntent != null) {
                try {
                    builder.addAction(android.R.drawable.ic_input_add, getString(R.string.reminder_add_entry), addEntryPendingIntent);
                } catch (Exception e) {
                    Log.e(TAG, "Failed to add action to notification", e);
                }
            }

            // Show the notification
            android.app.NotificationManager notificationManager = (android.app.NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                try {
                    Notification notification = builder.build();
                    notificationManager.notify(NOTIFICATION_ID + 1, notification); // Use different ID from foreground service
                    Log.d(TAG, "Notification displayed successfully with ID: " + (NOTIFICATION_ID + 1));

                } catch (Exception e) {
                    Log.e(TAG, "Failed to show notification", e);
                }
            } else {
                Log.e(TAG, "NotificationManager not available");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in showNotification", e);
        }
    }
}
