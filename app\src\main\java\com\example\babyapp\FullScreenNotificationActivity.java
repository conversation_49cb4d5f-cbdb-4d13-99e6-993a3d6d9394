package com.example.babyapp;

import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;


import androidx.appcompat.app.AppCompatActivity;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Full-screen notification activity that appears as a pop-up window
 * when baby care reminders are triggered
 */
public class FullScreenNotificationActivity extends BaseActivity {

    private static final String TAG = "FullScreenNotification";

    private TextView titleText;
    private TextView messageText;
    private TextView timeText;
    private Button dismissButton;
    private Button addEntryButton;
    private Button snoozeButton;

    private int intervalMinutes = 180; // Default 3 hours
    private long entryId = -1; // Entry ID for notification cancellation
    private MediaPlayer mediaPlayer;
    private Vibrator vibrator;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Check if we can safely show this activity
            if (!canShowFullScreenActivity()) {
                Log.w(TAG, "Cannot show full-screen activity safely, finishing");
                finish();
                return;
            }

            // Make this activity appear over lock screen and turn on screen
            setupFullScreenDisplay();

            setContentView(R.layout.activity_fullscreen_notification);

            // Initialize views
            initializeViews();

            // Get notification details from intent
            processNotificationIntent();

            // Setup button listeners
            setupButtonListeners();

            // Play notification sound and vibration (if enabled)
            playSoundIfEnabled();
            triggerVibrationIfEnabled();

            Log.d(TAG, "Full-screen notification activity created successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error creating full-screen notification activity", e);
            finish();
        }
    }

    /**
     * Check if we can safely show the full-screen activity
     */
    private boolean canShowFullScreenActivity() {
        try {
            // Check if activity is finishing or destroyed
            if (isFinishing() || isDestroyed()) {
                Log.w(TAG, "Activity is finishing or destroyed");
                return false;
            }

            // Check if we have permission to show over other apps (Android 6.0+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!android.provider.Settings.canDrawOverlays(this)) {
                    Log.w(TAG, "No permission to draw over other apps");
                    // Still allow showing, but without overlay capabilities
                }
            }

            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error checking if can show full-screen activity", e);
            return false;
        }
    }

    /**
     * Setup full-screen display properties to show over lock screen
     */
    private void setupFullScreenDisplay() {
        try {
            // Check if window is available
            if (getWindow() == null) {
                Log.e(TAG, "Window is null, cannot setup full-screen display");
                return;
            }

            // Show over lock screen (safer approach)
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                    setShowWhenLocked(true);
                    setTurnScreenOn(true);
                } else {
                    getWindow().addFlags(
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                        WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                    );
                }
            } catch (Exception e) {
                Log.e(TAG, "Error setting show when locked flags", e);
            }

            // Dismiss keyguard (safer approach)
            try {
                KeyguardManager keyguardManager = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
                if (keyguardManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    keyguardManager.requestDismissKeyguard(this, new KeyguardManager.KeyguardDismissCallback() {
                        @Override
                        public void onDismissError() {
                            Log.w(TAG, "Failed to dismiss keyguard");
                        }

                        @Override
                        public void onDismissSucceeded() {
                            Log.d(TAG, "Keyguard dismissed successfully");
                        }

                        @Override
                        public void onDismissCancelled() {
                            Log.w(TAG, "Keyguard dismiss cancelled");
                        }
                    });
                } else if (getWindow() != null) {
                    getWindow().addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error dismissing keyguard", e);
            }

            // Keep screen on while notification is displayed
            try {
                if (getWindow() != null) {
                    getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error setting keep screen on flag", e);
            }

            Log.d(TAG, "Full-screen display setup completed");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up full-screen display", e);
        }
    }

    /**
     * Initialize all UI components
     */
    private void initializeViews() {
        titleText = findViewById(R.id.notificationTitle);
        messageText = findViewById(R.id.notificationMessage);
        timeText = findViewById(R.id.notificationTime);
        dismissButton = findViewById(R.id.dismissButton);
        addEntryButton = findViewById(R.id.addEntryButton);
        snoozeButton = findViewById(R.id.snoozeButton);

        // Set current time with user's preferred format
        TimeFormatManager timeFormatManager = new TimeFormatManager(this);
        String currentTime = timeFormatManager.formatCurrentTime();
        timeText.setText(getString(R.string.reminder_current_time, currentTime));
    }

    /**
     * Process the notification intent to get details
     */
    private void processNotificationIntent() {
        try {
            Intent intent = getIntent();
            if (intent != null) {
                intervalMinutes = intent.getIntExtra("interval_minutes", 180);
                entryId = intent.getLongExtra("entry_id", -1);

                String title = intent.getStringExtra("title");
                String message = intent.getStringExtra("message");

                if (title != null) {
                    titleText.setText(title);
                } else {
                    titleText.setText(getString(R.string.reminder_title));
                }

                if (message != null) {
                    messageText.setText(message);
                } else {
                    messageText.setText(createDefaultMessage());
                }

                Log.d(TAG, "Processed notification intent: interval=" + intervalMinutes + " minutes, entryId=" + entryId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing notification intent", e);
            // Set default values
            titleText.setText(getString(R.string.reminder_title));
            messageText.setText(createDefaultMessage());
        }
    }

    /**
     * Create default notification message
     */
    private String createDefaultMessage() {
        if (intervalMinutes < 60) {
            return getString(R.string.reminder_minutes, intervalMinutes);
        } else {
            int hours = intervalMinutes / 60;
            int minutes = intervalMinutes % 60;

            if (minutes == 0) {
                if (hours == 1) {
                    return getString(R.string.reminder_1_hour);
                } else {
                    return getString(R.string.reminder_hours, hours);
                }
            } else {
                if (hours == 1) {
                    return getString(R.string.reminder_1_hour_minutes, minutes);
                } else {
                    return getString(R.string.reminder_hours_minutes, hours, minutes);
                }
            }
        }
    }

    /**
     * Setup button click listeners
     */
    private void setupButtonListeners() {
        dismissButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Dismiss button clicked");
                cancelCurrentNotification();
                finish();
            }
        });

        addEntryButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Add Entry button clicked");
                openAddEntryActivity();
            }
        });

        snoozeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Snooze button clicked");
                snoozeNotification();
            }
        });
    }

    /**
     * Open the Add Entry activity
     */
    private void openAddEntryActivity() {
        try {
            Intent intent = new Intent(this, AddEntryActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        } catch (Exception e) {
            Log.e(TAG, "Error opening Add Entry activity", e);
        }
    }

    /**
     * Cancel the current notification by entry ID
     */
    private void cancelCurrentNotification() {
        try {
            if (entryId != -1) {
                NotificationManager notificationManager = new NotificationManager(this);

                // Use the cancelNotification method with notification ID
                int notificationId = (int) entryId;
                notificationManager.cancelNotification(notificationId);

                Log.d(TAG, "Cancelled notification for entry ID: " + entryId);
            } else {
                Log.w(TAG, "No entry ID found, cannot cancel specific notification");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error cancelling current notification", e);
        }
    }

    /**
     * Snooze the notification for 15 minutes
     */
    private void snoozeNotification() {
        try {
            NotificationManager notificationManager = new NotificationManager(this);

            // Use the proper snooze method that maintains system consistency
            notificationManager.snoozeNotification(15); // 15 minutes

            Log.d(TAG, "Notification snoozed for 15 minutes using proper snooze method");
            finish();

        } catch (Exception e) {
            Log.e(TAG, "Error snoozing notification", e);
            finish();
        }
    }

    /**
     * Play notification sound to get user's attention
     */
    private void playNotificationSound() {
        try {
            // Get default notification sound
            Uri notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

            if (notificationUri == null) {
                // Fallback to alarm sound if notification sound not available
                notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM);
            }

            if (notificationUri == null) {
                // Final fallback to ringtone
                notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE);
            }

            if (notificationUri != null) {
                mediaPlayer = new MediaPlayer();

                // Set audio attributes for notification
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    AudioAttributes audioAttributes = new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .build();
                    mediaPlayer.setAudioAttributes(audioAttributes);
                } else {
                    mediaPlayer.setAudioStreamType(AudioManager.STREAM_NOTIFICATION);
                }

                mediaPlayer.setDataSource(this, notificationUri);
                mediaPlayer.setLooping(false);
                mediaPlayer.prepareAsync();

                mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                    @Override
                    public void onPrepared(MediaPlayer mp) {
                        try {
                            mp.start();
                            Log.d(TAG, "Notification sound started playing");
                        } catch (Exception e) {
                            Log.e(TAG, "Error starting notification sound", e);
                        }
                    }
                });

                mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        try {
                            mp.release();
                            mediaPlayer = null;
                            Log.d(TAG, "Notification sound completed and released");
                        } catch (Exception e) {
                            Log.e(TAG, "Error releasing media player", e);
                        }
                    }
                });

                mediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                    @Override
                    public boolean onError(MediaPlayer mp, int what, int extra) {
                        Log.e(TAG, "MediaPlayer error: what=" + what + ", extra=" + extra);
                        try {
                            mp.release();
                            mediaPlayer = null;
                        } catch (Exception e) {
                            Log.e(TAG, "Error releasing media player after error", e);
                        }
                        return true;
                    }
                });

            } else {
                Log.w(TAG, "No notification sound available on device");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error setting up notification sound", e);
        }
    }

    /**
     * Play sound if enabled in settings
     */
    private void playSoundIfEnabled() {
        try {
            // Check if sound is enabled in settings
            NotificationManager notificationManager = new NotificationManager(this);
            boolean soundEnabled = notificationManager.isSoundEnabled();

            Log.d(TAG, "Sound setting check: " + (soundEnabled ? "ENABLED" : "DISABLED"));

            if (!soundEnabled) {
                Log.i(TAG, "✓ Sound disabled by user in settings - notification will show WITHOUT sound");
                return;
            }

            Log.i(TAG, "✓ Sound enabled in settings - playing notification sound");
            playNotificationSound();

        } catch (Exception e) {
            Log.e(TAG, "Error checking sound setting", e);
        }
    }

    /**
     * Trigger vibration if enabled in settings
     */
    private void triggerVibrationIfEnabled() {
        try {
            // Check if vibration is enabled in settings
            NotificationManager notificationManager = new NotificationManager(this);
            boolean vibrationEnabled = notificationManager.isVibrationEnabled();

            Log.d(TAG, "Vibration setting check: " + (vibrationEnabled ? "ENABLED" : "DISABLED"));

            if (!vibrationEnabled) {
                Log.i(TAG, "✓ Vibration disabled by user in settings - notification will show WITHOUT vibration");
                return;
            }

            Log.i(TAG, "✓ Vibration enabled in settings - triggering vibration pattern");
            triggerVibration();

        } catch (Exception e) {
            Log.e(TAG, "Error checking vibration setting", e);
        }
    }

    /**
     * Trigger vibration to get user's attention
     */
    private void triggerVibration() {
        try {
            vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);

            if (vibrator != null && vibrator.hasVibrator()) {
                // Create vibration pattern: wait 0ms, vibrate 500ms, wait 200ms, vibrate 500ms
                long[] vibrationPattern = {0, 500, 200, 500};

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // Use VibrationEffect for newer Android versions
                    android.os.VibrationEffect vibrationEffect = android.os.VibrationEffect.createWaveform(vibrationPattern, -1);
                    vibrator.vibrate(vibrationEffect);
                } else {
                    // Use deprecated method for older Android versions
                    vibrator.vibrate(vibrationPattern, -1);
                }

                Log.d(TAG, "Vibration triggered successfully");
            } else {
                Log.w(TAG, "Vibrator not available on device");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error triggering vibration", e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clean up media player
        if (mediaPlayer != null) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
                mediaPlayer = null;
                Log.d(TAG, "Media player cleaned up");
            } catch (Exception e) {
                Log.e(TAG, "Error cleaning up media player", e);
            }
        }

        // Clean up vibrator
        if (vibrator != null) {
            try {
                vibrator.cancel();
                Log.d(TAG, "Vibration cancelled");
            } catch (Exception e) {
                Log.e(TAG, "Error cancelling vibration", e);
            }
        }

        Log.d(TAG, "Full-screen notification activity destroyed");
    }

    @Override
    public void onBackPressed() {
        // Allow back button to dismiss notification and cancel it
        Log.d(TAG, "Back button pressed - cancelling notification");
        cancelCurrentNotification();
        finish();
    }
}
