package com.example.babyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * Centralized time format management for the entire application
 * Handles 24-hour vs 12-hour time format based on user settings
 */
public class TimeFormatManager {
    
    private static final String TAG = "TimeFormatManager";
    private static final String PREFS_NAME = "BabyAppSettings";
    private static final String PREF_TIME_FORMAT = "time_format_24h";
    
    // Time format patterns
    private static final String PATTERN_24_HOUR = "HH:mm";
    private static final String PATTERN_12_HOUR = "h:mm a";
    private static final String PATTERN_24_HOUR_WITH_SECONDS = "HH:mm:ss";
    private static final String PATTERN_12_HOUR_WITH_SECONDS = "h:mm:ss a";
    private static final String PATTERN_24_HOUR_DATE_TIME = "MMM dd, yyyy HH:mm";
    private static final String PATTERN_12_HOUR_DATE_TIME = "MMM dd, yyyy h:mm a";
    
    private Context context;
    private SharedPreferences prefs;
    
    public TimeFormatManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    /**
     * Check if 24-hour format is enabled
     * @return true if 24-hour format is enabled, false for 12-hour format
     */
    public boolean is24HourFormat() {
        return prefs.getBoolean(PREF_TIME_FORMAT, false); // Default to 12-hour format
    }
    
    /**
     * Set time format preference
     * @param is24Hour true for 24-hour format, false for 12-hour format
     */
    public void setTimeFormat(boolean is24Hour) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(PREF_TIME_FORMAT, is24Hour);
        editor.apply();
        Log.d(TAG, "Time format set to: " + (is24Hour ? "24-hour" : "12-hour"));
    }
    
    /**
     * Get SimpleDateFormat for time only (HH:mm or h:mm a)
     * @return SimpleDateFormat configured for current time format preference
     */
    public SimpleDateFormat getTimeFormat() {
        String pattern = is24HourFormat() ? PATTERN_24_HOUR : PATTERN_12_HOUR;
        return new SimpleDateFormat(pattern, Locale.getDefault());
    }
    
    /**
     * Get SimpleDateFormat for time with seconds (HH:mm:ss or h:mm:ss a)
     * @return SimpleDateFormat configured for current time format preference with seconds
     */
    public SimpleDateFormat getTimeFormatWithSeconds() {
        String pattern = is24HourFormat() ? PATTERN_24_HOUR_WITH_SECONDS : PATTERN_12_HOUR_WITH_SECONDS;
        return new SimpleDateFormat(pattern, Locale.getDefault());
    }
    
    /**
     * Get SimpleDateFormat for date and time (MMM dd, yyyy HH:mm or MMM dd, yyyy h:mm a)
     * @return SimpleDateFormat configured for current time format preference with date
     */
    public SimpleDateFormat getDateTimeFormat() {
        String pattern = is24HourFormat() ? PATTERN_24_HOUR_DATE_TIME : PATTERN_12_HOUR_DATE_TIME;
        return new SimpleDateFormat(pattern, Locale.getDefault());
    }
    
    /**
     * Format time using current time format preference
     * @param date Date to format
     * @return Formatted time string
     */
    public String formatTime(Date date) {
        return getTimeFormat().format(date);
    }
    
    /**
     * Format time using current time format preference
     * @param timeMillis Time in milliseconds
     * @return Formatted time string
     */
    public String formatTime(long timeMillis) {
        return formatTime(new Date(timeMillis));
    }
    
    /**
     * Format time using current time format preference
     * @param calendar Calendar object to format
     * @return Formatted time string
     */
    public String formatTime(Calendar calendar) {
        return formatTime(calendar.getTime());
    }
    
    /**
     * Format time with seconds using current time format preference
     * @param date Date to format
     * @return Formatted time string with seconds
     */
    public String formatTimeWithSeconds(Date date) {
        return getTimeFormatWithSeconds().format(date);
    }
    
    /**
     * Format date and time using current time format preference
     * @param date Date to format
     * @return Formatted date and time string
     */
    public String formatDateTime(Date date) {
        return getDateTimeFormat().format(date);
    }
    
    /**
     * Format date and time using current time format preference
     * @param timeMillis Time in milliseconds
     * @return Formatted date and time string
     */
    public String formatDateTime(long timeMillis) {
        return formatDateTime(new Date(timeMillis));
    }
    
    /**
     * Format current time using current time format preference
     * @return Formatted current time string
     */
    public String formatCurrentTime() {
        return formatTime(new Date());
    }
    
    /**
     * Get time format description for UI display
     * @return User-friendly description of current time format
     */
    public String getTimeFormatDescription() {
        return is24HourFormat() ? "24-hour format" : "12-hour format";
    }
    
    /**
     * Get example time string for UI display
     * @return Example time in current format (e.g., "15:30" or "3:30 PM")
     */
    public String getTimeFormatExample() {
        Calendar example = Calendar.getInstance();
        example.set(Calendar.HOUR_OF_DAY, 15);
        example.set(Calendar.MINUTE, 30);
        return formatTime(example);
    }
    
    /**
     * Static helper method to get TimeFormatManager instance
     * @param context Application context
     * @return TimeFormatManager instance
     */
    public static TimeFormatManager getInstance(Context context) {
        return new TimeFormatManager(context);
    }
}
