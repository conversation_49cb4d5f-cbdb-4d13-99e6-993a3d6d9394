package com.example.babyapp;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Activity for displaying notification status and scheduled notifications
 */
public class NotificationListActivity extends BaseActivity {

    private static final String TAG = "NotificationListActivity";

    // UI Components
    private ImageView backButton;
    private TextView statusText;
    private LinearLayout emptyStateLayout;
    private RecyclerView notificationsRecyclerView;
    private Button testNotificationButton;
    private Button notificationSettingsButton;

    // Data
    private NotificationManager notificationManager;
    private List<NotificationManager.NotificationInfo> notifications;
    private NotificationAdapter adapter;

    // Activity Result Launcher for notification settings
    private ActivityResultLauncher<Intent> notificationSettingsLauncher;

    // Timer for updating notification status
    private Handler statusUpdateHandler;
    private Runnable statusUpdateRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notification_list);

        try {
            initializeActivityResultLaunchers();
            initializeComponents();
            setupClickListeners();
            loadNotificationData();
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            finish();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh data when returning from settings or other activities
        loadNotificationData();
        // Start status update timer
        startStatusUpdateTimer();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // Stop status update timer to save battery
        stopStatusUpdateTimer();
    }

    /**
     * Initialize Activity Result Launchers
     */
    private void initializeActivityResultLaunchers() {
        // Launcher for notification settings
        notificationSettingsLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        // Handle notification settings result
                        int intervalMinutes = result.getData().getIntExtra("interval_minutes", 180);
                        boolean vibrationEnabled = result.getData().getBooleanExtra("vibration_enabled", true);
                        boolean soundEnabled = result.getData().getBooleanExtra("sound_enabled", true);

                        // Save settings through notification manager
                        if (notificationManager != null) {
                            notificationManager.saveNotificationInterval(intervalMinutes);  // ✅ DODANO!
                            notificationManager.saveVibrationSetting(vibrationEnabled);
                            notificationManager.saveSoundSetting(soundEnabled);
                            Log.d(TAG, "Notification settings updated from main menu: interval=" + intervalMinutes + " minutes, vibration=" + vibrationEnabled + ", sound=" + soundEnabled);
                        }

                        // Refresh notification data to reflect any changes
                        loadNotificationData();
                    }
                }
            }
        );
    }

    /**
     * Initialize all UI components
     */
    private void initializeComponents() {
        try {
            backButton = findViewById(R.id.backButton);
            statusText = findViewById(R.id.statusText);
            emptyStateLayout = findViewById(R.id.emptyStateLayout);
            notificationsRecyclerView = findViewById(R.id.notificationsRecyclerView);
            testNotificationButton = findViewById(R.id.testNotificationButton);
            notificationSettingsButton = findViewById(R.id.notificationSettingsButton);

            // Initialize notification manager
            notificationManager = new NotificationManager(this);

            // Initialize data
            notifications = new ArrayList<>();

            // Setup RecyclerView
            if (notificationsRecyclerView != null) {
                notificationsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
                adapter = new NotificationAdapter(notifications, this::cancelNotification);
                notificationsRecyclerView.setAdapter(adapter);

                // Add swipe-to-delete functionality
                setupSwipeToDelete();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error initializing components", e);
            throw e;
        }
    }

    /**
     * Set up click listeners
     */
    private void setupClickListeners() {
        try {
            // Back button
            if (backButton != null) {
                backButton.setOnClickListener(v -> {
                    try {
                        finish();
                    } catch (Exception e) {
                        Log.e(TAG, "Error in back button click", e);
                    }
                });
            }

            // Test notification button
            if (testNotificationButton != null) {
                testNotificationButton.setOnClickListener(v -> {
                    try {
                        testNotification();
                    } catch (Exception e) {
                        Log.e(TAG, "Error in test notification", e);
                    }
                });
            }

            // Notification settings button
            if (notificationSettingsButton != null) {
                notificationSettingsButton.setOnClickListener(v -> {
                    try {
                        openNotificationSettings();
                    } catch (Exception e) {
                        Log.e(TAG, "Error opening notification settings", e);
                    }
                });
            }

        } catch (Exception e) {
            Log.e(TAG, "Error setting up click listeners", e);
        }
    }

    /**
     * Start timer to update notification status every second
     */
    private void startStatusUpdateTimer() {
        if (statusUpdateHandler == null) {
            statusUpdateHandler = new Handler();
        }

        if (statusUpdateRunnable == null) {
            statusUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    updateNotificationStatus();
                    // Schedule next update in 1 second
                    statusUpdateHandler.postDelayed(this, 1000);
                }
            };
        }

        // Start the timer
        statusUpdateHandler.post(statusUpdateRunnable);
        Log.d(TAG, "Status update timer started");
    }

    /**
     * Stop the status update timer
     */
    private void stopStatusUpdateTimer() {
        if (statusUpdateHandler != null && statusUpdateRunnable != null) {
            statusUpdateHandler.removeCallbacks(statusUpdateRunnable);
            Log.d(TAG, "Status update timer stopped");
        }
    }

    /**
     * Update only the notification status (called by timer)
     */
    private void updateNotificationStatus() {
        try {
            if (notificationManager != null && statusText != null) {
                String status = notificationManager.getNotificationStatus();
                statusText.setText(getString(R.string.status_label, status));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification status", e);
        }
    }

    /**
     * Load notification data and update UI (full refresh)
     */
    private void loadNotificationData() {
        try {
            // Get notification status
            String status = "Unknown";
            boolean hasNotifications = false;

            if (notificationManager != null) {
                try {
                    status = notificationManager.getNotificationStatus();
                    hasNotifications = notificationManager.isNotificationEnabled();
                } catch (Exception e) {
                    Log.e(TAG, "Error getting notification status", e);
                    status = getString(R.string.status_error);
                }
            }

            // Update status text with localized format
            if (statusText != null) {
                statusText.setText(getString(R.string.status_label, status));
            }

            // Get scheduled notifications
            List<NotificationManager.NotificationInfo> scheduledNotifications = notificationManager.getScheduledNotifications();

            // Show appropriate UI based on notification state
            if (!scheduledNotifications.isEmpty()) {
                showNotificationsList(scheduledNotifications);
            } else {
                showEmptyState();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error loading notification data", e);
            showEmptyState();
        }
    }

    /**
     * Show the notifications list
     */
    private void showNotificationsList(List<NotificationManager.NotificationInfo> scheduledNotifications) {
        try {
            if (emptyStateLayout != null) {
                emptyStateLayout.setVisibility(View.GONE);
            }
            if (notificationsRecyclerView != null) {
                notificationsRecyclerView.setVisibility(View.VISIBLE);
            }

            // Update notifications list with real data
            notifications.clear();
            notifications.addAll(scheduledNotifications);

            // Notify adapter of data change
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error showing notifications list", e);
            showEmptyState();
        }
    }

    /**
     * Show empty state when no notifications
     */
    private void showEmptyState() {
        try {
            if (notificationsRecyclerView != null) {
                notificationsRecyclerView.setVisibility(View.GONE);
            }
            if (emptyStateLayout != null) {
                emptyStateLayout.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing empty state", e);
        }
    }

    /**
     * Test notification functionality - shows full-screen notification
     */
    private void testNotification() {
        try {
            if (notificationManager != null) {
                // Test full-screen notification directly
                notificationManager.testFullScreenNotificationNow(180); // 3 hours for demo
                Log.d(TAG, "Test full-screen notification sent");
            } else {
                Log.w(TAG, "Notification manager not available");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error testing full-screen notification", e);
        }
    }

    /**
     * Open notification settings with current vibration setting
     */
    private void openNotificationSettings() {
        try {
            Intent intent = new Intent(this, NotificationSettingsActivity.class);

            // Pass current notification settings
            if (notificationManager != null) {
                // Get current interval (default to 3 hours if not set)
                int currentInterval = notificationManager.getNotificationInterval();
                if (currentInterval <= 0) {
                    currentInterval = 180; // Default 3 hours
                }

                // Get current vibration and sound settings
                boolean vibrationEnabled = notificationManager.isVibrationEnabled();
                boolean soundEnabled = notificationManager.isSoundEnabled();

                intent.putExtra("current_interval_minutes", currentInterval);
                intent.putExtra("vibration_enabled", vibrationEnabled);
                intent.putExtra("sound_enabled", soundEnabled);

                Log.d(TAG, "Opening notification settings from main menu with vibration: " + vibrationEnabled + ", sound: " + soundEnabled);
            }

            notificationSettingsLauncher.launch(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening notification settings", e);
        }
    }

    /**
     * Setup custom swipe-to-cancel functionality
     * Note: Custom swipe logic is now handled directly in NotificationViewHolder
     */
    private void setupSwipeToDelete() {
        // Custom swipe logic is now handled directly in NotificationViewHolder touch listeners
        // This method is kept for compatibility with existing code
    }

    /**
     * Cancel a specific notification (called from swipe gesture)
     */
    private void cancelNotification(NotificationManager.NotificationInfo notification, int position) {
        // Show confirmation dialog for swipe cancellation
        showCancelConfirmationDialog(notification, position);
    }

    /**
     * Cancel notification (overloaded method for adapter callback)
     */
    private void cancelNotification(NotificationManager.NotificationInfo notification) {
        int position = notifications.indexOf(notification);
        if (position >= 0) {
            showCancelConfirmationDialog(notification, position);
        }
    }

    /**
     * Show confirmation dialog before canceling notification
     */
    private void showCancelConfirmationDialog(NotificationManager.NotificationInfo notification, int position) {
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.cancel_notification_title));
            builder.setMessage(getString(R.string.cancel_notification_message));

            // Cancel button (dismiss dialog, keep notification)
            builder.setNegativeButton(getString(R.string.cancel_button), (dialog, which) -> {
                dialog.dismiss();
                // Hide cancel overlay since user cancelled
                hideCancelOverlayForPosition(position);
                Log.d(TAG, "Notification cancellation cancelled by user - overlay hidden");
            });

            // Confirm button (proceed with cancellation)
            builder.setPositiveButton(getString(R.string.confirm_button), (dialog, which) -> {
                dialog.dismiss();
                performNotificationCancellation(notification, position);
                Log.d(TAG, "Notification cancellation confirmed by user");
            });

            // Create and show dialog
            AlertDialog dialog = builder.create();
            dialog.show();

        } catch (Exception e) {
            Log.e(TAG, "Error showing cancel confirmation dialog", e);
            // Fallback to direct cancellation if dialog fails
            performNotificationCancellation(notification, position);
        }
    }

    /**
     * Hide cancel overlay for specific position
     */
    private void hideCancelOverlayForPosition(int position) {
        try {
            if (adapter != null && notificationsRecyclerView != null) {
                RecyclerView.ViewHolder viewHolder = notificationsRecyclerView.findViewHolderForAdapterPosition(position);
                if (viewHolder instanceof NotificationAdapter.NotificationViewHolder) {
                    NotificationAdapter.NotificationViewHolder notificationViewHolder = (NotificationAdapter.NotificationViewHolder) viewHolder;
                    notificationViewHolder.hideCancelOverlay();
                    // Reset state immediately for immediate next swipe capability
                    notificationViewHolder.resetOverlayState();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding cancel overlay", e);
        }
    }

    /**
     * Perform the actual notification cancellation
     */
    private void performNotificationCancellation(NotificationManager.NotificationInfo notification, int position) {
        try {
            // Cancel the notification through NotificationManager
            if (notificationManager != null) {
                notificationManager.cancelNotification(notification.id);
                Log.d(TAG, "Notification cancelled: " + notification.title);
            }

            // Remove from list
            notifications.remove(position);
            if (adapter != null) {
                adapter.notifyItemRemoved(position);
            }

            // Show empty state if no notifications left
            if (notifications.isEmpty()) {
                showEmptyState();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error performing notification cancellation", e);
            // Refresh the list to ensure consistency
            loadNotificationData();
        }
    }

    /**
     * Adapter for displaying notification items
     */
    private static class NotificationAdapter extends RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder> {

        private List<NotificationManager.NotificationInfo> notifications;
        private OnNotificationCancelListener cancelListener;

        public interface OnNotificationCancelListener {
            void onNotificationCancel(NotificationManager.NotificationInfo notification);
        }

        public NotificationAdapter(List<NotificationManager.NotificationInfo> notifications, OnNotificationCancelListener cancelListener) {
            this.notifications = notifications;
            this.cancelListener = cancelListener;
        }

        @Override
        public NotificationViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            android.view.View view = android.view.LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_notification, parent, false);
            return new NotificationViewHolder(view, cancelListener);
        }

        @Override
        public void onBindViewHolder(NotificationViewHolder holder, int position) {
            NotificationManager.NotificationInfo notification = notifications.get(position);
            holder.bind(notification);
        }

        @Override
        public int getItemCount() {
            return notifications.size();
        }

        static class NotificationViewHolder extends RecyclerView.ViewHolder {
            private TextView titleText;
            private TextView detailsText;
            private TextView timeText;
            private View statusIndicator;
            private ImageView notificationIcon;
            private ImageView cancelButton;
            private LinearLayout cancelOverlay;
            private LinearLayout notificationContent;
            private OnNotificationCancelListener cancelListener;
            private NotificationManager.NotificationInfo currentNotification;

            // Swipe detection variables
            private float initialX;
            private boolean isSwipeInProgress = false;
            private boolean isOverlayVisible = false;
            private static final float OVERLAY_THRESHOLD = 50f;

            public NotificationViewHolder(android.view.View itemView, OnNotificationCancelListener cancelListener) {
                super(itemView);
                this.cancelListener = cancelListener;

                titleText = itemView.findViewById(R.id.notificationTitle);
                detailsText = itemView.findViewById(R.id.notificationDetails);
                timeText = itemView.findViewById(R.id.notificationTime);
                statusIndicator = itemView.findViewById(R.id.statusIndicator);
                notificationIcon = itemView.findViewById(R.id.notificationIcon);
                cancelButton = itemView.findViewById(R.id.cancelButton);
                cancelOverlay = itemView.findViewById(R.id.cancelOverlay);
                notificationContent = itemView.findViewById(R.id.notificationContent);

                // Setup custom swipe gesture
                setupSwipeGesture();
            }

            public void bind(NotificationManager.NotificationInfo notification) {
                this.currentNotification = notification;

                titleText.setText(notification.title);
                detailsText.setText(notification.details);

                // Format time info using user's preferred format
                TimeFormatManager timeFormatManager = new TimeFormatManager(itemView.getContext());
                String formattedTime = timeFormatManager.formatTime(notification.scheduledTimeMillis);
                timeText.setText(itemView.getContext().getString(R.string.next_reminder, formattedTime));

                // Set cancel button click listener
                cancelButton.setOnClickListener(v -> {
                    if (cancelListener != null) {
                        cancelListener.onNotificationCancel(notification);
                    }
                });

                // Hide cancel overlay by default
                hideCancelOverlay();
                isOverlayVisible = false;
            }

            /**
             * Setup custom swipe gesture detection
             */
            private void setupSwipeGesture() {
                // Set up touch listener on the entire item view for better swipe detection
                itemView.setOnTouchListener(new View.OnTouchListener() {
                        @Override
                        public boolean onTouch(View v, MotionEvent event) {
                            switch (event.getAction()) {
                                case MotionEvent.ACTION_DOWN:
                                    initialX = event.getX();
                                    isSwipeInProgress = false;
                                    // Reset overlay state without animation to avoid conflicts
                                    isOverlayVisible = false;
                                    return true;

                                case MotionEvent.ACTION_MOVE:
                                    float deltaX = event.getX() - initialX;

                                    // Only allow swipe from right to left (negative deltaX) for cancel action
                                    if (deltaX < -50) { // Swipe left only
                                        isSwipeInProgress = true;

                                        // Show CANCEL overlay when swipe threshold is reached
                                        if (deltaX < -OVERLAY_THRESHOLD && !isOverlayVisible) {
                                            showCancelOverlay();
                                            isOverlayVisible = true;
                                        }
                                    } else if (deltaX > 50) { // Swipe right - no action
                                        isSwipeInProgress = true; // Mark as swipe but don't show overlay
                                    }
                                    return true;

                                case MotionEvent.ACTION_UP:
                                case MotionEvent.ACTION_CANCEL:
                                    float finalDeltaX = event.getX() - initialX;

                                    // Only trigger cancel dialog for left swipe (negative deltaX)
                                    if (finalDeltaX < -OVERLAY_THRESHOLD && isOverlayVisible) {
                                        // Left swipe threshold reached - trigger cancel dialog
                                        if (cancelListener != null && currentNotification != null) {
                                            cancelListener.onNotificationCancel(currentNotification);
                                        }
                                    } else {
                                        // Not enough left swipe or right swipe - hide overlay and reset
                                        hideCancelOverlay();
                                        isOverlayVisible = false;

                                        // Handle click if it wasn't a swipe
                                        if (!isSwipeInProgress) {
                                            v.performClick();
                                        }
                                    }

                                    isSwipeInProgress = false;
                                    return true;
                            }
                            return false;
                        }
                    });
            }

            /**
             * Show CANCEL overlay with fade-in animation
             */
            public void showCancelOverlay() {
                if (cancelOverlay != null) {
                    // Cancel any existing animation to prevent conflicts
                    cancelOverlay.animate().cancel();

                    cancelOverlay.setVisibility(View.VISIBLE);
                    cancelOverlay.setAlpha(0f);
                    cancelOverlay.animate()
                        .alpha(1f)
                        .setDuration(200)
                        .start();
                }
            }

            /**
             * Hide CANCEL overlay with fade-out animation
             */
            public void hideCancelOverlay() {
                if (cancelOverlay != null) {
                    // Reset state immediately for next swipe capability
                    resetOverlayState();

                    // Cancel any existing animation to prevent conflicts
                    cancelOverlay.animate().cancel();

                    cancelOverlay.animate()
                        .alpha(0f)
                        .setDuration(200)
                        .withEndAction(() -> {
                            cancelOverlay.setVisibility(View.GONE);
                            cancelOverlay.setAlpha(1f); // Reset for next time
                        })
                        .start();
                }
            }

            /**
             * Reset overlay state flags
             */
            public void resetOverlayState() {
                isOverlayVisible = false;
                isSwipeInProgress = false;
            }
        }
    }
}
