package com.example.babyapp;

import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * Dialog for PDF export configuration
 * Allows user to select columns and specify number of rows for PDF table generation
 */
public class PDFExportDialog extends DialogFragment {

    private static final String TAG = "PDFExportDialog";

    // UI Components
    private CheckBox poopColumnCheckbox;
    private CheckBox peeColumnCheckbox;
    private CheckBox formulaColumnCheckbox;
    private CheckBox nursingColumnCheckbox;
    private CheckBox burpingColumnCheckbox;
    private CheckBox notesColumnCheckbox;
    private EditText rowsEditText;
    private RadioGroup orientationRadioGroup;
    private RadioButton portraitRadio;
    private RadioButton landscapeRadio;
    private CheckBox includeCheckboxesCheckbox;
    private Button cancelButton;
    private Button generatePdfButton;

    // Listener interface for communication with parent activity
    public interface PDFExportListener {
        void onPDFExportRequested(List<String> selectedColumns, int numberOfRows, boolean isLandscape, boolean includeCheckboxes);
    }

    private PDFExportListener listener;

    /**
     * Set the listener for PDF export events
     */
    public void setPDFExportListener(PDFExportListener listener) {
        this.listener = listener;
    }

    /**
     * Get localized context based on saved language preference
     */
    private Context getLocalizedContext() {
        Context context = requireActivity();

        // Get saved language preference
        String savedLanguage = LocaleManager.getSavedLanguage(context);

        // Apply locale using LocaleManager and return localized context
        return LocaleManager.updateBaseContextLocale(context, savedLanguage);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        // Get localized context for strings
        Context localizedContext = getLocalizedContext();

        // Use requireActivity() for AlertDialog.Builder to maintain valid window token
        AlertDialog.Builder builder = new AlertDialog.Builder(requireActivity());

        // Use localized context for LayoutInflater to preserve localization
        // Theme colors will be inherited from the activity's theme
        LayoutInflater inflater = LayoutInflater.from(localizedContext);
        View view = inflater.inflate(R.layout.dialog_pdf_export, null);

        initializeViews(view);
        updateViewsWithLocalizedText(localizedContext);
        setupClickListeners();

        builder.setView(view);
        return builder.create();
    }

    /**
     * Initialize all UI components
     */
    private void initializeViews(View view) {
        poopColumnCheckbox = view.findViewById(R.id.poopColumnCheckbox);
        peeColumnCheckbox = view.findViewById(R.id.peeColumnCheckbox);
        formulaColumnCheckbox = view.findViewById(R.id.formulaColumnCheckbox);
        nursingColumnCheckbox = view.findViewById(R.id.nursingColumnCheckbox);
        burpingColumnCheckbox = view.findViewById(R.id.burpingColumnCheckbox);
        notesColumnCheckbox = view.findViewById(R.id.notesColumnCheckbox);
        rowsEditText = view.findViewById(R.id.rowsEditText);
        orientationRadioGroup = view.findViewById(R.id.orientationRadioGroup);
        portraitRadio = view.findViewById(R.id.portraitRadio);
        landscapeRadio = view.findViewById(R.id.landscapeRadio);
        includeCheckboxesCheckbox = view.findViewById(R.id.includeCheckboxesCheckbox);
        cancelButton = view.findViewById(R.id.cancelButton);
        generatePdfButton = view.findViewById(R.id.generatePdfButton);

        Log.d(TAG, "PDF Export Dialog views initialized");
    }

    /**
     * Update views with localized text from localized context
     */
    private void updateViewsWithLocalizedText(Context localizedContext) {
        // Note: Since layout is already inflated with localized context,
        // the text should already be localized. This method is kept for
        // potential future manual text updates if needed.
        Log.d(TAG, "Views updated with localized text");
    }

    /**
     * Setup click listeners for buttons
     */
    private void setupClickListeners() {
        cancelButton.setOnClickListener(v -> {
            Log.d(TAG, "PDF Export cancelled by user");
            dismiss();
        });

        generatePdfButton.setOnClickListener(v -> {
            handleGeneratePDF();
        });
    }

    /**
     * Handle PDF generation request
     */
    private void handleGeneratePDF() {
        try {
            // Get selected columns
            List<String> selectedColumns = getSelectedColumns();

            // Get page orientation
            boolean isLandscape = landscapeRadio.isChecked();

            // Get checkbox inclusion preference
            boolean includeCheckboxes = includeCheckboxesCheckbox.isChecked();

            // Get number of rows
            String rowsText = rowsEditText.getText().toString().trim();
            if (rowsText.isEmpty()) {
                showError(getString(R.string.pdf_error_enter_rows));
                return;
            }

            int numberOfRows;
            try {
                numberOfRows = Integer.parseInt(rowsText);
                if (numberOfRows <= 0) {
                    showError(getString(R.string.pdf_error_positive_number));
                    return;
                }

                // Validate maximum rows based on orientation
                int maxRows = getMaxRows(isLandscape);
                if (numberOfRows > maxRows) {
                    if (isLandscape) {
                        showError(getString(R.string.pdf_error_max_rows_landscape, maxRows));
                    } else {
                        showError(getString(R.string.pdf_error_max_rows_portrait, maxRows));
                    }
                    return;
                }
            } catch (NumberFormatException e) {
                showError(getString(R.string.pdf_error_valid_number));
                return;
            }

            // Validate that at least one column is selected (besides Time)
            if (selectedColumns.size() <= 1) { // Only Time is always selected
                showError(getString(R.string.pdf_error_select_column));
                return;
            }

            Log.d(TAG, "PDF Export requested - Columns: " + selectedColumns + ", Rows: " + numberOfRows + ", Landscape: " + isLandscape + ", Include Checkboxes: " + includeCheckboxes);

            // Notify listener
            if (listener != null) {
                listener.onPDFExportRequested(selectedColumns, numberOfRows, isLandscape, includeCheckboxes);
            }

            dismiss();

        } catch (Exception e) {
            Log.e(TAG, "Error handling PDF generation", e);
            showError(getString(R.string.pdf_error_preparing));
        }
    }

    /**
     * Get list of selected columns
     */
    private List<String> getSelectedColumns() {
        List<String> columns = new ArrayList<>();

        // Time is always included
        columns.add(getString(R.string.pdf_column_time));

        if (poopColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_poop));
        }
        if (peeColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_pee));
        }
        if (formulaColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_formula));
        }
        if (nursingColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_nursing));
        }
        if (burpingColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_burping));
        }

        if (notesColumnCheckbox.isChecked()) {
            columns.add(getString(R.string.pdf_column_notes));
        }

        return columns;
    }

    /**
     * Get maximum number of rows based on page orientation
     */
    private int getMaxRows(boolean isLandscape) {
        return isLandscape ? 19 : 30;
    }

    /**
     * Show error message to user
     */
    private void showError(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Log.d(TAG, "PDF Export Dialog attached to context");
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listener = null;
        Log.d(TAG, "PDF Export Dialog detached from context");
    }
}
