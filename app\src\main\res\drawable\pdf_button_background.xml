<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/light_pink" />
            <corners android:radius="18dp" />
            <stroke android:width="1dp" android:color="@color/white" />
        </shape>
    </item>
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/light_pink" />
            <corners android:radius="18dp" />
            <stroke android:width="1dp" android:color="@color/white" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="18dp" />
            <stroke android:width="1dp" android:color="@color/light_pink" />
        </shape>
    </item>
</selector>
