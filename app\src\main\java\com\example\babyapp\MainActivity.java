package com.example.babyapp;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.annotation.NonNull;
import android.app.DatePickerDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import com.google.android.material.bottomsheet.BottomSheetDialog;

/**
 * Main activity for the Baby App
 * Features: Baby journal with scrollable entries, calendar picker, settings navigation
 * Extends BaseActivity for automatic locale management
 */
public class MainActivity extends BaseActivity {

    // Constants
    private static final String DATE_FORMAT_PATTERN = "MMM dd, yyyy";
    private static final String SELECTED_DATE_KEY = "selected_date";

    // UI Components
    private TextView showingCounterText;
    private LinearLayout filterChipsContainer;
    private LinearLayout filterChipsLayout;
    private ImageView filterIcon;
    private Button addNewItemButton;
    private ImageView sortButton;
    private TextView dateText;
    private ImageView settingsIcon;
    private ImageView notificationsIcon;
    private RecyclerView entriesRecyclerView;
    private LinearLayout emptyStateLayout;

    // Multi-select UI components
    private LinearLayout multiSelectLayout;
    private Button deleteAllButton;
    private CheckBox selectAllCheckbox;

    // Data
    private Calendar selectedDate;
    private SimpleDateFormat dateFormat;
    private List<BabyCareEntry> babyCareEntries;
    private List<BabyCareEntry> filteredEntries;
    private BabyCareAdapter adapter;
    private FilterCriteria currentFilter;

    // Activity Result Launchers
    private ActivityResultLauncher<Intent> addEntryLauncher;
    private ActivityResultLauncher<Intent> editEntryLauncher;
    private ActivityResultLauncher<Intent> filterLauncher;

    // Notification system
    private NotificationManager notificationManager;
    private NotificationBadgeManager badgeManager;
    private MissedNotificationTracker missedNotificationTracker;
    private boolean isShowingMissedNotificationPopup = false;
    private BroadcastReceiver inAppNotificationReceiver;

    // Data persistence
    private DataManager dataManager;

    // Sort management
    private SortManager sortManager;

    // Multi-select mode
    private boolean isMultiSelectMode = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Check if this is first launch and permissions need to be requested
        if (!PermissionRequestActivity.hasRequestedPermissions(this)) {
            Log.d("MainActivity", "First launch detected, showing permission request");
            Intent permissionIntent = new Intent(this, PermissionRequestActivity.class);
            startActivity(permissionIntent);
            finish(); // Close MainActivity so user can't go back
            return;
        }

        // Initialize Activity Result Launchers
        initializeActivityResultLaunchers();

        // Hide only the action bar, keep system status bar visible
        hideActionBar();

        try {
            setContentView(R.layout.activity_main);

            // Validate layout inflation
            if (!validateLayoutInflation()) {
                Log.e("MainActivity", "Layout inflation failed or incomplete");
                return;
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error setting content view", e);
            return;
        }

        // Initialize notification manager with error handling
        try {
            notificationManager = new NotificationManager(this);
        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing notification manager", e);
            // Continue without notification manager - app should still work
        }

        // Initialize badge manager
        try {
            badgeManager = new NotificationBadgeManager(this);
        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing badge manager", e);
        }

        // Initialize missed notification tracker
        try {
            missedNotificationTracker = new MissedNotificationTracker(this);
            Log.d("MainActivity", "MissedNotificationTracker initialized successfully");
        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing missed notification tracker", e);
            missedNotificationTracker = null; // Ensure it's null if initialization fails
        }

        // Initialize data persistence
        try {
            dataManager = DataManager.getInstance(this);
            Log.d("MainActivity", "Data manager initialized successfully");
        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing data manager", e);
        }

        // Check if app was restarted due to language change
        handleLanguageChangeRestart();

        // Check if app was opened from notification and clear badges
        handleNotificationOpen();

        // Check for missed notifications if not opened from notification
        checkForMissedNotifications();

        // Setup in-app notification receiver
        setupInAppNotificationReceiver();

        initializeComponents();
        restoreInstanceState(savedInstanceState);
        setupClickListeners();
        updateDateDisplay();
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putLong(SELECTED_DATE_KEY, selectedDate.getTimeInMillis());

        // Save activity list when instance state is saved
        saveActivityList();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d("MainActivity", "onPause called - saving activity list");

        // Save activity list when activity is paused
        saveActivityList();
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d("MainActivity", "onStop called - saving activity list");

        // Save activity list when activity is stopped
        saveActivityList();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d("MainActivity", "onDestroy called - saving activity list");

        // Unregister in-app notification receiver
        if (inAppNotificationReceiver != null) {
            try {
                androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(this)
                    .unregisterReceiver(inAppNotificationReceiver);
                Log.d("MainActivity", "In-app notification receiver unregistered");
            } catch (Exception e) {
                Log.e("MainActivity", "Error unregistering in-app notification receiver", e);
            }
        }

        // Save activity list when activity is destroyed
        saveActivityList();
    }

    /**
     * Initialize Activity Result Launchers for modern Android API
     */
    private void initializeActivityResultLaunchers() {
        // Launcher for adding new entries
        addEntryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        BabyCareEntry newEntry = (BabyCareEntry) result.getData().getSerializableExtra("new_entry");
                        if (newEntry != null) {
                            addNewEntry(newEntry);
                        }
                    }
                }
            }
        );

        // Launcher for editing entries
        editEntryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        BabyCareEntry updatedEntry = (BabyCareEntry) result.getData().getSerializableExtra("updated_entry");
                        int position = result.getData().getIntExtra("edit_position", -1);
                        if (updatedEntry != null && position >= 0 && position < babyCareEntries.size()) {
                            babyCareEntries.set(position, updatedEntry);
                            applyCurrentFilter(); // Refresh filtered list
                            updateShowingCounter();

                            // Save the updated list to persistent storage
                            saveActivityList();
                            Log.d("MainActivity", "Entry updated and saved to storage");
                        }
                    }
                }
            }
        );

        // Launcher for filter activity
        filterLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    try {
                        if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                            FilterCriteria filterCriteria = (FilterCriteria) result.getData().getSerializableExtra("filter_criteria");
                            if (filterCriteria != null) {
                                applyFilter(filterCriteria);
                            } else {
                                // If no filter criteria, clear filters
                                currentFilter = null;
                                applyCurrentFilter();
                            }
                        }
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error processing filter result", e);
                    }
                }
            }
        );
    }

    /**
     * Initialize date format, calendar, and UI components
     */
    private void initializeComponents() {
        // Initialize date format and calendar
        dateFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.getDefault());
        selectedDate = Calendar.getInstance();

        // Initialize views with error handling
        try {
            showingCounterText = findViewById(R.id.showingCounterText);
            filterChipsContainer = findViewById(R.id.filterChipsContainer);
            filterChipsLayout = findViewById(R.id.filterChipsLayout);
            filterIcon = findViewById(R.id.filterIcon);
            addNewItemButton = findViewById(R.id.addNewItemButton);
            sortButton = findViewById(R.id.sortButton);
            dateText = findViewById(R.id.dateText);
            settingsIcon = findViewById(R.id.settingsIcon);
            notificationsIcon = findViewById(R.id.notificationsIcon);
            entriesRecyclerView = findViewById(R.id.entriesRecyclerView);
            emptyStateLayout = findViewById(R.id.emptyStateLayout);

            // Multi-select UI components
            multiSelectLayout = findViewById(R.id.multiSelectLayout);
            deleteAllButton = findViewById(R.id.deleteAllButton);
            selectAllCheckbox = findViewById(R.id.selectAllCheckbox);

            // Validate critical views
            if (notificationsIcon == null) {
                Log.e("MainActivity", "notificationsIcon is null after findViewById");
            }
            if (settingsIcon == null) {
                Log.e("MainActivity", "settingsIcon is null after findViewById");
            }
            if (filterIcon == null) {
                Log.e("MainActivity", "filterIcon is null after findViewById");
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing views", e);
        }

        // Initialize data and adapter
        babyCareEntries = new ArrayList<>();
        filteredEntries = new ArrayList<>();
        currentFilter = null;

        // Initialize sort manager
        try {
            sortManager = new SortManager(this);
            Log.d("MainActivity", "Sort manager initialized successfully");
        } catch (Exception e) {
            Log.e("MainActivity", "Error initializing sort manager", e);
        }

        // Load saved activity list from persistent storage
        loadSavedActivityList();
        adapter = new BabyCareAdapter(filteredEntries, new BabyCareAdapter.OnEntryActionListener() {
            @Override
            public void onEditEntry(BabyCareEntry entry, int position) {
                editEntry(entry, position);
            }

            @Override
            public void onDeleteEntry(BabyCareEntry entry, int position) {
                deleteEntry(entry, position);
            }
        });

        // Set multi-select listener
        adapter.setMultiSelectListener(new BabyCareAdapter.OnMultiSelectListener() {
            @Override
            public void onMultiSelectModeStarted() {
                enterMultiSelectMode();
            }

            @Override
            public void onMultiSelectModeEnded() {
                exitMultiSelectMode();
            }

            @Override
            public void onSelectionChanged(int selectedCount) {
                updateSelectAllCheckbox();
                updateShowingCounter();
            }

            @Override
            public void onLongPress(BabyCareEntry entry, int position) {
                startMultiSelectMode(position);
            }
        });

        // Setup RecyclerView
        entriesRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        entriesRecyclerView.setAdapter(adapter);

        // Setup swipe-to-delete functionality
        adapter.setupSwipeToDelete(entriesRecyclerView);

        // Initialize filtered entries with all entries (no filter applied initially)
        try {
            applyCurrentFilter();
        } catch (Exception e) {
            // If filter initialization fails, just show all entries
            if (filteredEntries == null) {
                filteredEntries = new ArrayList<>();
            }
            filteredEntries.addAll(babyCareEntries);
        }

        // Update empty state and counter
        updateEmptyState();
        updateShowingCounter();
    }

    /**
     * Restore saved instance state if available
     */
    private void restoreInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            long dateMillis = savedInstanceState.getLong(SELECTED_DATE_KEY, System.currentTimeMillis());
            selectedDate.setTimeInMillis(dateMillis);
        }
    }

    /**
     * Set up click listeners for all interactive elements
     */
    private void setupClickListeners() {
        try {
            // Filter icon click listener
            if (filterIcon != null) {
                filterIcon.setOnClickListener(v -> filterEntries());
            } else {
                Log.e("MainActivity", "filterIcon is null, cannot set click listener");
            }

            // Settings icon click listener
            if (settingsIcon != null) {
                settingsIcon.setOnClickListener(v -> openSettings());
            } else {
                Log.e("MainActivity", "settingsIcon is null, cannot set click listener");
            }

            // Notifications icon click listener
            if (notificationsIcon != null) {
                notificationsIcon.setOnClickListener(v -> {
                    try {
                        openNotifications();
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error in notifications click handler", e);
                    }
                });
            } else {
                Log.e("MainActivity", "notificationsIcon is null, cannot set click listener");
                // Create a fallback notification access method
                createFallbackNotificationAccess();
            }

            // Add New Item button click listener
            if (addNewItemButton != null) {
                addNewItemButton.setOnClickListener(v -> addNewItem());
            } else {
                Log.e("MainActivity", "addNewItemButton is null, cannot set click listener");
            }

            // Sort button click listener
            if (sortButton != null) {
                sortButton.setOnClickListener(v -> sortItems());
            } else {
                Log.e("MainActivity", "sortButton is null, cannot set click listener");
            }

            // Multi-select button listeners
            if (deleteAllButton != null) {
                deleteAllButton.setOnClickListener(v -> showDeleteSelectedDialog());
            }

            if (selectAllCheckbox != null) {
                selectAllCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    if (isChecked) {
                        adapter.selectAll();
                    } else {
                        adapter.clearSelection();
                    }
                });
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up click listeners", e);
        }
    }

    /**
     * Update the date display with formatted date
     */
    private void updateDateDisplay() {
        String formattedDate = dateFormat.format(selectedDate.getTime());
        dateText.setText(formattedDate);
    }

    /**
     * Filter entries functionality - opens FilterActivity
     */
    private void filterEntries() {
        Intent intent = new Intent(this, FilterActivity.class);
        // Pass current filter to maintain selections
        if (currentFilter != null) {
            intent.putExtra("current_filter", currentFilter);
        }

        // Pass date range for custom range initialization
        if (babyCareEntries != null && !babyCareEntries.isEmpty()) {
            // Find first and last entry dates
            Date firstDate = null;
            Date lastDate = null;

            for (BabyCareEntry entry : babyCareEntries) {
                Date entryDate = entry.getDateCreated();
                if (entryDate != null) {
                    if (firstDate == null || entryDate.before(firstDate)) {
                        firstDate = entryDate;
                    }
                    if (lastDate == null || entryDate.after(lastDate)) {
                        lastDate = entryDate;
                    }
                }
            }

            if (firstDate != null && lastDate != null) {
                intent.putExtra("first_entry_date", firstDate.getTime());
                intent.putExtra("last_entry_date", lastDate.getTime());
            }
        }

        filterLauncher.launch(intent);
    }

    /**
     * Open settings activity
     */
    private void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
    }

    /**
     * Handle app restart due to settings changes - show success message
     */
    private void handleLanguageChangeRestart() {
        try {
            Intent intent = getIntent();
            if (intent != null) {
                boolean languageChanged = intent.getBooleanExtra("language_changed", false);
                boolean timeFormatChanged = intent.getBooleanExtra("time_format_changed", false);
                boolean bothChanged = intent.getBooleanExtra("both_changed", false);
                boolean settingsChanged = intent.getBooleanExtra("settings_changed", false);

                if (bothChanged || settingsChanged) {
                    Log.d("MainActivity", "App restarted due to multiple settings changes");

                    // Show generic success message for multiple changes
                    new Handler().postDelayed(() -> {
                        Toast.makeText(this, getString(R.string.all_changes_saved), Toast.LENGTH_LONG).show();
                    }, 500);

                    // Remove flags
                    intent.removeExtra("both_changed");
                    intent.removeExtra("settings_changed");
                    intent.removeExtra("language_changed");
                    intent.removeExtra("time_format_changed");

                } else if (languageChanged) {
                    Log.d("MainActivity", "App restarted due to language change - showing success message");

                    // Show success toast message
                    new Handler().postDelayed(() -> {
                        Toast.makeText(this, getString(R.string.language_changed_toast), Toast.LENGTH_LONG).show();
                    }, 500);

                    // Remove the flag to prevent showing on subsequent recreations
                    intent.removeExtra("language_changed");

                } else if (timeFormatChanged) {
                    Log.d("MainActivity", "App restarted due to time format change - showing success message");

                    // Show success toast message
                    new Handler().postDelayed(() -> {
                        Toast.makeText(this, getString(R.string.time_format_changed_toast), Toast.LENGTH_LONG).show();
                    }, 500);

                    // Remove the flag to prevent showing on subsequent recreations
                    intent.removeExtra("time_format_changed");
                }
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error handling restart", e);
        }
    }

    /**
     * Handle app opening from notification - clear badges if needed
     * IMPORTANT: This method should NEVER refresh or clear activity data
     */
    private void handleNotificationOpen() {
        try {
            Intent intent = getIntent();
            if (intent != null && intent.getBooleanExtra("opened_from_notification", false)) {
                Log.d("MainActivity", "App opened from notification - clearing notification badges (preserving activity list)");

                // Clear notification badges (this doesn't affect activity data)
                if (badgeManager != null) {
                    badgeManager.clearBabyCareNotificationBadges();
                    Log.d("MainActivity", "Notification badges cleared successfully");
                } else {
                    Log.w("MainActivity", "Badge manager is null, cannot clear badges");
                }

                // Clear missed notifications since user opened from notification (this doesn't affect activity data)
                if (missedNotificationTracker != null) {
                    missedNotificationTracker.clearMissedNotifications();
                    Log.d("MainActivity", "Missed notifications cleared - opened from notification");
                }

                // IMPORTANT: Preserve activity list data - do NOT refresh or reload
                Log.d("MainActivity", "Activity list preserved - " + (babyCareEntries != null ? babyCareEntries.size() : 0) + " entries maintained");

                // Remove the flag to prevent clearing on subsequent activity recreations
                intent.removeExtra("opened_from_notification");
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error handling notification open", e);
        }
    }

    /**
     * Check for missed notifications and show pop-up if needed
     * IMPORTANT: This method should NEVER refresh or clear activity data
     */
    private void checkForMissedNotifications() {
        try {
            Intent intent = getIntent();
            boolean openedFromNotification = intent != null && intent.getBooleanExtra("opened_from_notification", false);

            // Only check for missed notifications if NOT opened from notification
            if (!openedFromNotification && missedNotificationTracker != null) {
                if (missedNotificationTracker.shouldShowMissedNotificationPopup()) {
                    Log.d("MainActivity", "Showing missed notification pop-up (preserving activity list)");
                    // Mark popup as shown BEFORE showing it to prevent duplicates
                    missedNotificationTracker.markPopupShownForCurrentNotification();
                    showMissedNotificationPopup();
                } else {
                    Log.d("MainActivity", "No missed notifications to show");
                    // Record that app was opened normally
                    missedNotificationTracker.recordAppOpened(System.currentTimeMillis());
                }

                // IMPORTANT: Preserve activity list data - do NOT refresh or reload
                Log.d("MainActivity", "Activity list preserved during missed notification check - " +
                      (babyCareEntries != null ? babyCareEntries.size() : 0) + " entries maintained");
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error checking for missed notifications", e);
        }
    }

    /**
     * Show pop-up dialog for missed notifications
     */
    private void showMissedNotificationPopup() {
        try {
            if (missedNotificationTracker == null) {
                Log.w("MainActivity", "Missed notification tracker is null");
                return;
            }

            if (isFinishing() || isDestroyed()) {
                Log.w("MainActivity", "Activity is finishing or destroyed, cannot show pop-up");
                return;
            }

            // Prevent showing multiple popups simultaneously
            if (isShowingMissedNotificationPopup) {
                Log.w("MainActivity", "Missed notification popup already showing, skipping duplicate");
                return;
            }

            String message;
            try {
                message = missedNotificationTracker.getMissedNotificationMessage();
                if (message == null || message.trim().isEmpty()) {
                    message = getString(R.string.missed_notification_default);
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Error getting missed notification message", e);
                message = getString(R.string.missed_notification_default);
            }

            try {
                // Set flag to prevent duplicate popups
                isShowingMissedNotificationPopup = true;

                android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
                builder.setTitle(getString(R.string.scheduled_notification_title))
                       .setMessage(message)
                       .setPositiveButton(getString(R.string.ok_button), (dialog, which) -> {
                           try {
                               // Clear missed notifications when user acknowledges
                               if (missedNotificationTracker != null) {
                                   missedNotificationTracker.clearMissedNotifications();
                                   Log.d("MainActivity", "Missed notifications cleared by user");
                               }
                               if (dialog != null) {
                                   dialog.dismiss();
                               }
                           } catch (Exception e) {
                               Log.e("MainActivity", "Error in OK button click handler", e);
                           } finally {
                               // Clear flag when dialog is dismissed
                               isShowingMissedNotificationPopup = false;
                           }
                       })
                       .setCancelable(false) // Force user to acknowledge
                       .setOnDismissListener(dialog -> {
                           // Clear flag when dialog is dismissed (backup)
                           isShowingMissedNotificationPopup = false;
                       });

                android.app.AlertDialog dialog = builder.create();
                if (dialog != null && !isFinishing() && !isDestroyed()) {
                    dialog.show();
                    Log.d("MainActivity", "Missed notification pop-up displayed (single instance)");
                } else {
                    Log.w("MainActivity", "Cannot show dialog - activity state invalid");
                    // Clear flag if dialog couldn't be shown
                    isShowingMissedNotificationPopup = false;
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Error creating or showing AlertDialog", e);
                // Clear flag on error
                isShowingMissedNotificationPopup = false;
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error showing missed notification pop-up", e);
        }
    }

    /**
     * Open notifications functionality
     */
    private void openNotifications() {
        try {
            Log.d("MainActivity", "Opening notifications screen");
            Intent intent = new Intent(this, NotificationListActivity.class);
            startActivity(intent);
        } catch (Exception e) {
            Log.e("MainActivity", "Error opening notifications screen", e);
        }
    }

    /**
     * Add new item functionality - opens AddEntryActivity
     */
    private void addNewItem() {
        Intent intent = new Intent(this, AddEntryActivity.class);
        addEntryLauncher.launch(intent);
    }

    /**
     * Sort items functionality - shows bottom sheet with sort options
     */
    private void sortItems() {
        try {
            if (sortManager == null) {
                Log.e("MainActivity", "Sort manager not initialized");
                return;
            }

            // Create bottom sheet dialog
            BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
            View bottomSheetView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_sort, null);
            bottomSheetDialog.setContentView(bottomSheetView);

            // Get views from bottom sheet
            LinearLayout sortByDateLayout = bottomSheetView.findViewById(R.id.sortByDateLayout);
            LinearLayout sortByTimeLayout = bottomSheetView.findViewById(R.id.sortByTimeLayout);
            ImageView dateSortIcon = bottomSheetView.findViewById(R.id.dateSortIcon);
            ImageView timeSortIcon = bottomSheetView.findViewById(R.id.timeSortIcon);

            // Update icons based on current sort state
            updateSortIcons(dateSortIcon, timeSortIcon);

            // Set click listeners
            sortByDateLayout.setOnClickListener(v -> {
                handleDateSort();
                updateSortIcons(dateSortIcon, timeSortIcon);
                bottomSheetDialog.dismiss();
            });

            sortByTimeLayout.setOnClickListener(v -> {
                handleTimeSort();
                updateSortIcons(dateSortIcon, timeSortIcon);
                bottomSheetDialog.dismiss();
            });

            // Show bottom sheet
            bottomSheetDialog.show();

        } catch (Exception e) {
            Log.e("MainActivity", "Error showing sort bottom sheet", e);
        }
    }

    /**
     * Handle date sort - toggle direction if already sorting by date, clear if same direction, otherwise sort descending
     */
    private void handleDateSort() {
        try {
            if (sortManager.getCurrentSortType() == SortManager.SortType.DATE) {
                // Already sorting by date, toggle direction
                SortManager.SortDirection currentDirection = sortManager.getCurrentSortDirection();
                if (currentDirection == SortManager.SortDirection.DESCENDING) {
                    // Currently descending, switch to ascending
                    SortManager.SortDirection direction = sortManager.toggleDirection();
                    List<BabyCareEntry> sortedEntries = sortManager.sortByDate(filteredEntries, direction);
                    filteredEntries.clear();
                    filteredEntries.addAll(sortedEntries);
                    Log.d("MainActivity", "Sorted by date " + direction);
                } else {
                    // Currently ascending, clear sort
                    sortManager.clearSort();
                    applyCurrentFilter(); // Restore original order
                    Log.d("MainActivity", "Date sort cleared");
                }
            } else {
                // Not sorting by date, start with descending (newest first)
                SortManager.SortDirection direction = SortManager.SortDirection.DESCENDING;
                List<BabyCareEntry> sortedEntries = sortManager.sortByDate(filteredEntries, direction);
                filteredEntries.clear();
                filteredEntries.addAll(sortedEntries);
                Log.d("MainActivity", "Sorted by date " + direction);
            }

            // Update adapter
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error handling date sort", e);
        }
    }

    /**
     * Handle time sort - toggle direction if already sorting by time, clear if same direction, otherwise sort descending
     */
    private void handleTimeSort() {
        try {
            if (sortManager.getCurrentSortType() == SortManager.SortType.TIME) {
                // Already sorting by time, toggle direction
                SortManager.SortDirection currentDirection = sortManager.getCurrentSortDirection();
                if (currentDirection == SortManager.SortDirection.DESCENDING) {
                    // Currently descending, switch to ascending
                    SortManager.SortDirection direction = sortManager.toggleDirection();
                    List<BabyCareEntry> sortedEntries = sortManager.sortByTime(filteredEntries, direction);
                    filteredEntries.clear();
                    filteredEntries.addAll(sortedEntries);
                    Log.d("MainActivity", "Sorted by time " + direction);
                } else {
                    // Currently ascending, clear sort
                    sortManager.clearSort();
                    applyCurrentFilter(); // Restore original order
                    Log.d("MainActivity", "Time sort cleared");
                }
            } else {
                // Not sorting by time, start with descending (latest time first)
                SortManager.SortDirection direction = SortManager.SortDirection.DESCENDING;
                List<BabyCareEntry> sortedEntries = sortManager.sortByTime(filteredEntries, direction);
                filteredEntries.clear();
                filteredEntries.addAll(sortedEntries);
                Log.d("MainActivity", "Sorted by time " + direction);
            }

            // Update adapter
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error handling time sort", e);
        }
    }

    /**
     * Update sort icons based on current sort state
     */
    private void updateSortIcons(ImageView dateSortIcon, ImageView timeSortIcon) {
        try {
            if (sortManager == null || dateSortIcon == null || timeSortIcon == null) {
                return;
            }

            SortManager.SortType currentType = sortManager.getCurrentSortType();
            SortManager.SortDirection currentDirection = sortManager.getCurrentSortDirection();

            // Hide both icons by default (use INVISIBLE to maintain layout space)
            dateSortIcon.setVisibility(View.INVISIBLE);
            timeSortIcon.setVisibility(View.INVISIBLE);

            // Show and set appropriate icon only for current active sort type
            if (currentType == SortManager.SortType.DATE) {
                dateSortIcon.setVisibility(View.VISIBLE);
                dateSortIcon.setImageResource(
                    currentDirection == SortManager.SortDirection.ASCENDING ?
                    R.drawable.arrow_icon : R.drawable.arrow_icon_down
                );
            } else if (currentType == SortManager.SortType.TIME) {
                timeSortIcon.setVisibility(View.VISIBLE);
                timeSortIcon.setImageResource(
                    currentDirection == SortManager.SortDirection.ASCENDING ?
                    R.drawable.arrow_icon : R.drawable.arrow_icon_down
                );
            }
            // If currentType is NONE, both icons remain invisible but maintain layout space

        } catch (Exception e) {
            Log.e("MainActivity", "Error updating sort icons", e);
        }
    }

    /**
     * Add new entry to the list
     */
    private void addNewEntry(BabyCareEntry entry) {
        babyCareEntries.add(0, entry); // Add to beginning of main list
        applyCurrentFilter(); // Refresh filtered list
        updateEmptyState();
        updateShowingCounter();

        // Scroll to top to show the new entry
        entriesRecyclerView.scrollToPosition(0);

        // Save the updated list to persistent storage
        saveActivityList();
        Log.d("MainActivity", "New entry added and saved to storage");
    }

    /**
     * Edit an existing entry
     */
    private void editEntry(BabyCareEntry entry, int position) {
        // Find the entry in the main list
        int mainListPosition = babyCareEntries.indexOf(entry);
        Intent intent = new Intent(this, AddEntryActivity.class);
        intent.putExtra("edit_entry", entry);
        intent.putExtra("edit_position", mainListPosition);
        editEntryLauncher.launch(intent);
    }

    /**
     * Delete an entry with confirmation dialog
     */
    private void deleteEntry(BabyCareEntry entry, int position) {
        showDeleteConfirmationDialog(entry, position);
    }

    /**
     * Show confirmation dialog before deleting entry
     */
    private void showDeleteConfirmationDialog(BabyCareEntry entry, int position) {
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.delete_entry_title));
            builder.setMessage(getString(R.string.delete_entry_message));

            // Cancel button (dismiss dialog, keep entry)
            builder.setNegativeButton(getString(R.string.cancel_button), (dialog, which) -> {
                dialog.dismiss();
                // Hide delete overlay since user cancelled
                hideDeleteOverlayForPosition(position);
                Log.d("MainActivity", "Entry deletion cancelled by user - overlay hidden");
            });

            // Confirm button (proceed with deletion)
            builder.setPositiveButton(getString(R.string.confirm_button), (dialog, which) -> {
                dialog.dismiss();
                performEntryDeletion(entry, position);
                Log.d("MainActivity", "Entry deletion confirmed by user");
            });

            // Create and show dialog
            AlertDialog dialog = builder.create();
            dialog.show();

        } catch (Exception e) {
            Log.e("MainActivity", "Error showing delete confirmation dialog", e);
            // Fallback to direct deletion if dialog fails
            performEntryDeletion(entry, position);
        }
    }

    /**
     * Hide delete overlay for specific position
     */
    private void hideDeleteOverlayForPosition(int position) {
        try {
            if (adapter != null && entriesRecyclerView != null) {
                RecyclerView.ViewHolder viewHolder = entriesRecyclerView.findViewHolderForAdapterPosition(position);
                if (viewHolder instanceof BabyCareAdapter.ViewHolder) {
                    BabyCareAdapter.ViewHolder babyCareViewHolder = (BabyCareAdapter.ViewHolder) viewHolder;
                    babyCareViewHolder.hideDeleteOverlay();
                    // Reset state immediately for immediate next swipe capability
                    babyCareViewHolder.resetOverlayState();
                }
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error hiding delete overlay", e);
        }
    }

    /**
     * Perform the actual entry deletion
     */
    private void performEntryDeletion(BabyCareEntry entry, int position) {
        try {
            // Cancel any notification associated with this entry
            if (notificationManager != null) {
                notificationManager.cancelNotificationForEntry(entry);
                Log.d("MainActivity", "Notification cancelled for deleted entry");
            }

            // Remove from main list
            babyCareEntries.remove(entry);
            // Refresh filtered list
            applyCurrentFilter();
            updateEmptyState();
            updateShowingCounter();

            // Save the updated list to persistent storage
            saveActivityList();
            Log.d("MainActivity", "Entry deleted and saved to storage");

        } catch (Exception e) {
            Log.e("MainActivity", "Error performing entry deletion", e);
        }
    }

    /**
     * Update empty state visibility
     */
    private void updateEmptyState() {
        if (filteredEntries.isEmpty()) {
            emptyStateLayout.setVisibility(View.VISIBLE);
            entriesRecyclerView.setVisibility(View.GONE);
        } else {
            emptyStateLayout.setVisibility(View.GONE);
            entriesRecyclerView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Update showing counter text
     */
    private void updateShowingCounter() {
        String counterText;

        if (isMultiSelectMode && adapter != null) {
            // Multi-select mode - show selected count
            int selectedCount = adapter.getSelectedCount();
            int totalCount = filteredEntries.size();

            if (selectedCount == 0) {
                counterText = getString(R.string.showing_entries_default);
            } else {
                counterText = getString(R.string.selected_count, selectedCount, totalCount);
            }
        } else {
            // Normal mode - show total count
            int count = filteredEntries.size();

            if (count == 0) {
                counterText = getString(R.string.showing_entries_default);
            } else {
                counterText = getString(R.string.entries_count, count);
            }
        }

        showingCounterText.setText(counterText);
    }

    /**
     * Apply filter criteria to the entries list
     */
    private void applyFilter(FilterCriteria filterCriteria) {
        try {
            currentFilter = filterCriteria;

            // Update filter chips
            updateFilterChips();

            // Apply actual filtering logic
            applyCurrentFilter();

        } catch (Exception e) {
            Log.e("MainActivity", "Error applying filters", e);
        }
    }

    /**
     * Apply current filter to the entries list
     */
    private void applyCurrentFilter() {
        try {
            if (filteredEntries == null) {
                filteredEntries = new ArrayList<>();
                Log.d("MainActivity", "Initialized filteredEntries list during filter application");
            }

            int originalSize = babyCareEntries != null ? babyCareEntries.size() : 0;
            filteredEntries.clear();

            if (babyCareEntries == null) {
                babyCareEntries = new ArrayList<>();
                Log.d("MainActivity", "Initialized babyCareEntries list during filter application");
            }

            if (currentFilter == null || !currentFilter.hasFilters()) {
                // No filter applied, show all entries
                filteredEntries.addAll(babyCareEntries);
                Log.d("MainActivity", "Applied no filter - showing all " + babyCareEntries.size() + " entries (preserved from " + originalSize + ")");
            } else {
                // Apply filters
                for (BabyCareEntry entry : babyCareEntries) {
                    if (entry != null && matchesFilter(entry, currentFilter)) {
                        filteredEntries.add(entry);
                    }
                }
                Log.d("MainActivity", "Applied filter - showing " + filteredEntries.size() + " of " + babyCareEntries.size() + " entries (preserved from " + originalSize + ")");
            }

            // Notify adapter of data change
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
            updateEmptyState();
            updateShowingCounter();
        } catch (Exception e) {
            Log.e("MainActivity", "Error applying filter", e);
            // Handle any errors gracefully - NEVER lose existing data
            if (filteredEntries == null) {
                filteredEntries = new ArrayList<>();
            }
            if (babyCareEntries != null && !babyCareEntries.isEmpty()) {
                filteredEntries.clear();
                filteredEntries.addAll(babyCareEntries);
                Log.d("MainActivity", "Filter error recovery - preserved all " + babyCareEntries.size() + " entries");
            }
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
            updateEmptyState();
            updateShowingCounter();
        }
    }

    /**
     * Check if an entry matches the current filter criteria
     */
    private boolean matchesFilter(BabyCareEntry entry, FilterCriteria filter) {
        // Date filter
        if (filter.getSelectedDateFilter() != null) {
            if (!matchesDateFilter(entry, filter.getSelectedDateFilter(), filter)) {
                return false;
            }
        }

        // Diaper filter
        if (filter.getSelectedDiaperFilter() != null) {
            if (!matchesDiaperFilter(entry, filter.getSelectedDiaperFilter())) {
                return false;
            }
        }

        // Formula filter
        if (filter.getSelectedFormulaFilter() != null) {
            if (!matchesFormulaFilter(entry, filter)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if entry matches date filter
     */
    private boolean matchesDateFilter(BabyCareEntry entry, String dateFilter, FilterCriteria filterCriteria) {
        Calendar entryDate = Calendar.getInstance();
        entryDate.setTime(entry.getDateCreated());

        Calendar today = Calendar.getInstance();

        switch (dateFilter) {
            case FilterCriteria.DATE_TODAY:
                return isSameDay(entryDate, today);

            case FilterCriteria.DATE_THIS_WEEK:
                Calendar weekStart = Calendar.getInstance();
                weekStart.set(Calendar.DAY_OF_WEEK, weekStart.getFirstDayOfWeek());
                weekStart.set(Calendar.HOUR_OF_DAY, 0);
                weekStart.set(Calendar.MINUTE, 0);
                weekStart.set(Calendar.SECOND, 0);
                weekStart.set(Calendar.MILLISECOND, 0);

                Calendar weekEnd = Calendar.getInstance();
                weekEnd.setTime(weekStart.getTime());
                weekEnd.add(Calendar.DAY_OF_WEEK, 6);
                weekEnd.set(Calendar.HOUR_OF_DAY, 23);
                weekEnd.set(Calendar.MINUTE, 59);
                weekEnd.set(Calendar.SECOND, 59);

                return entry.getDateCreated().after(weekStart.getTime()) &&
                       entry.getDateCreated().before(weekEnd.getTime());

            case FilterCriteria.DATE_THIS_MONTH:
                return entryDate.get(Calendar.YEAR) == today.get(Calendar.YEAR) &&
                       entryDate.get(Calendar.MONTH) == today.get(Calendar.MONTH);

            case FilterCriteria.DATE_CUSTOM_RANGE:
                return matchesCustomDateRange(entry, filterCriteria);

            default:
                return true;
        }
    }

    /**
     * Check if entry matches custom date range
     */
    private boolean matchesCustomDateRange(BabyCareEntry entry, FilterCriteria filterCriteria) {
        Date fromDate = filterCriteria.getCustomFromDate();
        Date toDate = filterCriteria.getCustomToDate();

        if (fromDate == null || toDate == null) {
            return true; // No custom range set
        }

        Date entryDate = entry.getDateCreated();
        if (entryDate == null) {
            return false;
        }

        // Smart date range: if "To" date is before "From" date, swap them
        Date actualFromDate = fromDate;
        Date actualToDate = toDate;

        if (toDate.before(fromDate)) {
            actualFromDate = toDate;
            actualToDate = fromDate;
        }

        if (filterCriteria.isDateRange()) {
            // Date range: entry date should be between actual from and to dates (inclusive)
            Calendar fromCal = Calendar.getInstance();
            fromCal.setTime(actualFromDate);
            fromCal.set(Calendar.HOUR_OF_DAY, 0);
            fromCal.set(Calendar.MINUTE, 0);
            fromCal.set(Calendar.SECOND, 0);
            fromCal.set(Calendar.MILLISECOND, 0);

            Calendar toCal = Calendar.getInstance();
            toCal.setTime(actualToDate);
            toCal.set(Calendar.HOUR_OF_DAY, 23);
            toCal.set(Calendar.MINUTE, 59);
            toCal.set(Calendar.SECOND, 59);
            toCal.set(Calendar.MILLISECOND, 999);

            return entryDate.compareTo(fromCal.getTime()) >= 0 &&
                   entryDate.compareTo(toCal.getTime()) <= 0;
        } else {
            // Exact date: entry date should match the from date (ignoring time)
            Calendar entryCal = Calendar.getInstance();
            entryCal.setTime(entryDate);

            Calendar fromCal = Calendar.getInstance();
            fromCal.setTime(actualFromDate);

            return entryCal.get(Calendar.YEAR) == fromCal.get(Calendar.YEAR) &&
                   entryCal.get(Calendar.MONTH) == fromCal.get(Calendar.MONTH) &&
                   entryCal.get(Calendar.DAY_OF_MONTH) == fromCal.get(Calendar.DAY_OF_MONTH);
        }
    }

    /**
     * Check if entry matches diaper filter
     */
    private boolean matchesDiaperFilter(BabyCareEntry entry, String diaperFilter) {
        switch (diaperFilter) {
            case FilterCriteria.DIAPER_POOP_ONLY:
                return entry.isPoop() && !entry.isPee();

            case FilterCriteria.DIAPER_PEE_ONLY:
                return entry.isPee() && !entry.isPoop();

            case FilterCriteria.DIAPER_ANY:
                return entry.isPoop() || entry.isPee();

            default:
                return true;
        }
    }

    /**
     * Check if entry matches formula filter
     */
    private boolean matchesFormulaFilter(BabyCareEntry entry, FilterCriteria filterCriteria) {
        if (!entry.isFormula()) {
            return false; // Entry doesn't have formula, so it doesn't match any formula filter
        }

        String formulaFilter = filterCriteria.getSelectedFormulaFilter();
        int amount = entry.getFormulaAmount();

        switch (formulaFilter) {
            case FilterCriteria.FORMULA_ANY:
                // Check if custom amount is specified
                Integer customAmount = filterCriteria.getCustomFormulaAmount();
                if (customAmount != null) {
                    return amount == customAmount;
                } else {
                    return true; // Any formula amount if no custom amount specified
                }

            case FilterCriteria.FORMULA_LESS_100:
                return amount < 100;

            case FilterCriteria.FORMULA_MORE_200:
                return amount > 200;

            default:
                return true;
        }
    }

    /**
     * Check if two calendar dates are the same day
     */
    private boolean isSameDay(Calendar cal1, Calendar cal2) {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * Update filter chips display
     */
    private void updateFilterChips() {
        // Safety check for null components
        if (filterChipsLayout == null || filterChipsContainer == null) {
            return;
        }

        // Clear existing chips
        filterChipsLayout.removeAllViews();

        if (currentFilter == null || !currentFilter.hasFilters()) {
            // No filters, hide container
            filterChipsContainer.setVisibility(View.GONE);
            return;
        }

        // Show container and add chips for each active filter
        filterChipsContainer.setVisibility(View.VISIBLE);

        try {
            for (String filterName : currentFilter.getIndividualFilters()) {
                addFilterChip(filterName);
            }
        } catch (Exception e) {
            // Handle any inflation errors gracefully
            filterChipsContainer.setVisibility(View.GONE);
        }
    }

    /**
     * Add a single filter chip to the layout
     */
    private void addFilterChip(String filterName) {
        try {
            // Inflate the filter chip layout
            View chipView = getLayoutInflater().inflate(R.layout.filter_chip, filterChipsLayout, false);

            TextView chipText = chipView.findViewById(R.id.filterChipText);
            ImageView chipClose = chipView.findViewById(R.id.filterChipClose);

            if (chipText != null && chipClose != null) {
                // Use localized filter name for display
                String localizedFilterName = getLocalizedFilterName(filterName);
                chipText.setText(localizedFilterName);

                // Set click listener for the close button (use original filterName for removal)
                chipClose.setOnClickListener(v -> removeFilter(filterName));

                // Add the chip to the layout
                filterChipsLayout.addView(chipView);
            }
        } catch (Exception e) {
            // If chip creation fails, create a simple fallback
            createFallbackFilterChip(filterName);
        }
    }

    /**
     * Create a simple fallback filter chip if inflation fails
     */
    private void createFallbackFilterChip(String filterName) {
        try {
            TextView fallbackChip = new TextView(this);
            // Use localized filter name for display
            String localizedFilterName = getLocalizedFilterName(filterName);
            fallbackChip.setText(localizedFilterName + " ✕");
            fallbackChip.setPadding(24, 12, 24, 12);
            fallbackChip.setBackgroundColor(getResources().getColor(R.color.baby_pink, null));
            fallbackChip.setTextColor(getResources().getColor(R.color.white, null));
            fallbackChip.setTextSize(12);

            // Set margins
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            params.setMargins(0, 0, 16, 0);
            fallbackChip.setLayoutParams(params);

            // Set click listener to remove filter (use original filterName)
            fallbackChip.setOnClickListener(v -> removeFilter(filterName));

            filterChipsLayout.addView(fallbackChip);
        } catch (Exception e) {
            // If even fallback fails, just ignore
        }
    }

    /**
     * Get localized filter name for display
     */
    private String getLocalizedFilterName(String filterName) {
        // Map hardcoded English filter names to localized string resources
        switch (filterName) {
            // Date filters
            case "Today":
                return getString(R.string.filter_chip_today);
            case "This Week":
                return getString(R.string.filter_chip_this_week);
            case "This Month":
                return getString(R.string.filter_chip_this_month);
            case "Custom Range":
                // For custom range, show the actual date range if available
                if (currentFilter != null && currentFilter.getCustomFromDate() != null && currentFilter.getCustomToDate() != null) {
                    Date fromDate = currentFilter.getCustomFromDate();
                    Date toDate = currentFilter.getCustomToDate();

                    // Smart date handling: if "To" is before "From", swap them for display
                    Date actualFromDate = fromDate;
                    Date actualToDate = toDate;

                    if (toDate.before(fromDate)) {
                        actualFromDate = toDate;
                        actualToDate = fromDate;
                    }

                    SimpleDateFormat chipDateFormat = new SimpleDateFormat("MMM dd", Locale.getDefault());
                    String fromDateStr = chipDateFormat.format(actualFromDate);
                    String toDateStr = chipDateFormat.format(actualToDate);

                    // Check if dates are the same - show only one date
                    Calendar fromCal = Calendar.getInstance();
                    fromCal.setTime(actualFromDate);
                    Calendar toCal = Calendar.getInstance();
                    toCal.setTime(actualToDate);

                    if (fromCal.get(Calendar.YEAR) == toCal.get(Calendar.YEAR) &&
                        fromCal.get(Calendar.MONTH) == toCal.get(Calendar.MONTH) &&
                        fromCal.get(Calendar.DAY_OF_MONTH) == toCal.get(Calendar.DAY_OF_MONTH)) {
                        // Same date - show only one
                        return fromDateStr;
                    } else {
                        // Different dates - show range
                        return getString(R.string.filter_custom_range_display, fromDateStr, toDateStr);
                    }
                } else {
                    return getString(R.string.filter_chip_custom_range);
                }

            // Diaper filters
            case "Poop Only":
                return getString(R.string.filter_chip_poop_only);
            case "Pee Only":
                return getString(R.string.filter_chip_pee_only);
            case "Any Diaper Change":
                return getString(R.string.filter_chip_any_diaper);

            // Formula filters
            case "Any Amount":
                // For "Any Amount", show custom amount if specified
                if (currentFilter != null && currentFilter.getCustomFormulaAmount() != null) {
                    return getString(R.string.filter_chip_custom_amount, currentFilter.getCustomFormulaAmount());
                } else {
                    return getString(R.string.filter_chip_any_amount);
                }
            case "Less than 100ml":
                return getString(R.string.filter_chip_less_100ml);
            case "More than 200ml":
                return getString(R.string.filter_chip_more_200ml);

            // Fallback to original name if not found
            default:
                return filterName;
        }
    }

    /**
     * Remove a specific filter and update the display
     */
    private void removeFilter(String filterName) {
        try {
            if (currentFilter != null && filterName != null) {
                currentFilter.removeFilter(filterName);

                // Update display
                updateFilterChips();
                applyCurrentFilter();
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error removing filter", e);
        }
    }

    /**
     * Hide only the action bar, keep system status bar visible
     */
    private void hideActionBar() {
        // Hide the action bar only (not the system status bar)
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
    }

    /**
     * Create fallback notification access when icon is missing
     */
    private void createFallbackNotificationAccess() {
        try {
            Log.d("MainActivity", "Creating fallback notification access");

            // Add a long-press listener to the app title as fallback
            TextView appTitle = findViewById(R.id.appTitleText);
            if (appTitle != null) {
                appTitle.setOnLongClickListener(v -> {
                    try {
                        openNotifications();
                        return true;
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error in fallback notification access", e);
                        return false;
                    }
                });
                Log.d("MainActivity", "Fallback notification access created - long press app title");
            } else {
                Log.e("MainActivity", "Cannot create fallback - app title not found");
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error creating fallback notification access", e);
        }
    }

    /**
     * Validate that critical layout elements are properly inflated
     */
    private boolean validateLayoutInflation() {
        try {
            // Check for critical views that must exist
            View topBar = findViewById(R.id.topBar);
            View actionButtonsLayout = findViewById(R.id.actionButtonsLayout);
            View entriesRecyclerView = findViewById(R.id.entriesRecyclerView);

            if (topBar == null) {
                Log.e("MainActivity", "topBar not found in layout");
                return false;
            }

            if (actionButtonsLayout == null) {
                Log.e("MainActivity", "actionButtonsLayout not found in layout");
                return false;
            }

            if (entriesRecyclerView == null) {
                Log.e("MainActivity", "entriesRecyclerView not found in layout");
                return false;
            }

            // Check for notification icon specifically
            View notificationIcon = findViewById(R.id.notificationsIcon);
            if (notificationIcon == null) {
                Log.w("MainActivity", "notificationsIcon not found in layout - will use fallback");
            }

            Log.d("MainActivity", "Layout validation passed");
            return true;

        } catch (Exception e) {
            Log.e("MainActivity", "Error validating layout inflation", e);
            return false;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d("MainActivity", "onResume called");

        try {
            // Check if app was opened from notification
            Intent intent = getIntent();
            boolean openedFromNotification = intent != null && intent.getBooleanExtra("opened_from_notification", false);

            if (openedFromNotification) {
                Log.d("MainActivity", "App resumed from notification - skipping data refresh to preserve activity list");
                // Only handle notification-specific logic, don't refresh data
                handleNotificationOpen();
            } else {
                Log.d("MainActivity", "App resumed normally - checking for missed notifications");
                // Check for missed notifications when app comes back from background normally
                checkForMissedNotifications();

                // Only refresh the list when returning normally (not from notification)
                // This prevents clearing the activity list when user gets notifications
                refreshEntryList();
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error in onResume", e);
            // Fallback: just check for missed notifications without refreshing
            try {
                checkForMissedNotifications();
            } catch (Exception e2) {
                Log.e("MainActivity", "Error in fallback onResume logic", e2);
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d("MainActivity", "onNewIntent called - app brought to front");

        // Update the intent and handle notification open
        setIntent(intent);

        // Check if this is from a notification
        if (intent != null && intent.getBooleanExtra("opened_from_notification", false)) {
            Log.d("MainActivity", "App brought to front from notification - preserving existing activity list");
            Log.d("MainActivity", "Current activity list size: " + (babyCareEntries != null ? babyCareEntries.size() : 0));

            // Handle notification open (clear badges, etc.) but preserve data
            handleNotificationOpen();

            // Ensure UI is updated with current data
            if (adapter != null) {
                adapter.notifyDataSetChanged();
                Log.d("MainActivity", "Adapter notified of data changes");
            }
            updateEmptyState();
            updateShowingCounter();
        } else {
            Log.d("MainActivity", "App brought to front normally");
        }
    }

    /**
     * Refresh the entry list from storage
     * IMPORTANT: This method loads data from persistent storage
     */
    private void refreshEntryList() {
        try {
            Log.d("MainActivity", "Entry list refresh requested - loading from storage");

            // SAFETY CHECK: Never clear existing data without replacement
            if (babyCareEntries == null) {
                babyCareEntries = new ArrayList<>();
                Log.d("MainActivity", "Initialized empty babyCareEntries list");
            }

            if (filteredEntries == null) {
                filteredEntries = new ArrayList<>();
                Log.d("MainActivity", "Initialized empty filteredEntries list");
            }

            // Load data from persistent storage
            int originalSize = babyCareEntries.size();
            loadSavedActivityList();

            // Apply current filter and update UI
            applyCurrentFilter();
            updateEmptyState();
            updateShowingCounter();

            Log.d("MainActivity", "Entry list refresh completed - loaded " + babyCareEntries.size() + " entries (was " + originalSize + ")");

        } catch (Exception e) {
            Log.e("MainActivity", "Error refreshing entry list", e);
            // SAFETY: Ensure lists are never null even if refresh fails
            if (babyCareEntries == null) {
                babyCareEntries = new ArrayList<>();
            }
            if (filteredEntries == null) {
                filteredEntries = new ArrayList<>();
            }
        }
    }

    /**
     * Load saved activity list from persistent storage
     */
    private void loadSavedActivityList() {
        try {
            if (dataManager == null) {
                Log.w("MainActivity", "Data manager not initialized, cannot load activity list");
                return;
            }

            List<BabyCareEntry> loadedEntries = dataManager.getActivityList();
            if (loadedEntries != null && !loadedEntries.isEmpty()) {
                babyCareEntries.clear();
                babyCareEntries.addAll(loadedEntries);
                Log.d("MainActivity", "Successfully loaded " + loadedEntries.size() + " entries from DataManager");
            } else {
                Log.d("MainActivity", "No saved activity list found - starting with empty list");
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error loading saved activity list", e);
            // Ensure we have a valid list even if loading fails
            if (babyCareEntries == null) {
                babyCareEntries = new ArrayList<>();
            }
        }
    }

    /**
     * Save current activity list to persistent storage
     */
    private void saveActivityList() {
        try {
            if (dataManager == null) {
                Log.w("MainActivity", "Data manager not initialized, cannot save activity list");
                return;
            }

            dataManager.setActivityList(babyCareEntries);
            Log.d("MainActivity", "Activity list saved to DataManager");
        } catch (Exception e) {
            Log.e("MainActivity", "Error saving activity list", e);
        }
    }

    /**
     * Setup receiver for in-app notifications
     */
    private void setupInAppNotificationReceiver() {
        try {
            inAppNotificationReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    try {
                        String title = intent.getStringExtra("title");
                        String message = intent.getStringExtra("message");
                        int intervalMinutes = intent.getIntExtra("interval_minutes", 180);

                        Log.d("MainActivity", "Received in-app notification: " + title);

                        // Show full-screen notification within the app
                        showInAppFullScreenNotification(title, message, intervalMinutes);

                    } catch (Exception e) {
                        Log.e("MainActivity", "Error handling in-app notification", e);
                    }
                }
            };

            // Register receiver for local broadcasts
            androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(this)
                .registerReceiver(inAppNotificationReceiver,
                    new IntentFilter("com.example.babyapp.IN_APP_NOTIFICATION"));

            Log.d("MainActivity", "In-app notification receiver registered");

        } catch (Exception e) {
            Log.e("MainActivity", "Error setting up in-app notification receiver", e);
        }
    }

    /**
     * Show full-screen notification within the app (no task switching)
     */
    private void showInAppFullScreenNotification(String title, String message, int intervalMinutes) {
        try {
            Intent intent = new Intent(this, FullScreenNotificationActivity.class);
            intent.putExtra("title", title);
            intent.putExtra("message", message);
            intent.putExtra("interval_minutes", intervalMinutes);

            // Use flags that don't create new task - stays within app
            intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);

            startActivity(intent);
            Log.d("MainActivity", "In-app full-screen notification started");

        } catch (Exception e) {
            Log.e("MainActivity", "Error showing in-app full-screen notification", e);
        }
    }

    // ==================== MULTI-SELECT FUNCTIONALITY ====================

    /**
     * Start multi-select mode when user long presses an item
     */
    private void startMultiSelectMode(int position) {
        isMultiSelectMode = true;
        adapter.setMultiSelectMode(true);

        // Select the long-pressed item
        adapter.getSelectedPositions().add(position);

        enterMultiSelectMode();
    }

    /**
     * Enter multi-select mode - show multi-select UI
     */
    private void enterMultiSelectMode() {
        if (multiSelectLayout != null) {
            multiSelectLayout.setVisibility(View.VISIBLE);
        }
        updateSelectAllCheckbox();
        updateShowingCounter();
    }

    /**
     * Exit multi-select mode - hide multi-select UI
     */
    private void exitMultiSelectMode() {
        isMultiSelectMode = false;
        adapter.setMultiSelectMode(false);

        if (multiSelectLayout != null) {
            multiSelectLayout.setVisibility(View.GONE);
        }

        if (selectAllCheckbox != null) {
            selectAllCheckbox.setChecked(false);
        }

        updateShowingCounter();
    }

    /**
     * Update select all checkbox based on current selection
     */
    private void updateSelectAllCheckbox() {
        if (selectAllCheckbox != null && adapter != null) {
            int selectedCount = adapter.getSelectedCount();
            int totalCount = filteredEntries.size();

            if (selectedCount == 0) {
                selectAllCheckbox.setChecked(false);
            } else if (selectedCount == totalCount) {
                selectAllCheckbox.setChecked(true);
            } else {
                // Partially selected - could use indeterminate state
                selectAllCheckbox.setChecked(false);
            }
        }
    }

    /**
     * Show delete confirmation dialog for selected entries
     */
    private void showDeleteSelectedDialog() {
        if (adapter == null) return;

        int selectedCount = adapter.getSelectedCount();
        if (selectedCount == 0) {
            Toast.makeText(this, "No entries selected", Toast.LENGTH_SHORT).show();
            return;
        }

        String title;
        String message;

        if (selectedCount == filteredEntries.size()) {
            // Deleting all entries
            title = getString(R.string.delete_all_title);
            message = getString(R.string.delete_all_message, selectedCount);
        } else {
            // Deleting selected entries
            title = getString(R.string.delete_selected_title);
            message = getString(R.string.delete_selected_message, selectedCount);
        }

        new AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(R.string.confirm_button, (dialog, which) -> {
                deleteSelectedEntries();
            })
            .setNegativeButton(R.string.cancel_button, null)
            .show();
    }

    /**
     * Delete all selected entries
     */
    private void deleteSelectedEntries() {
        if (adapter == null) return;

        List<BabyCareEntry> selectedEntries = adapter.getSelectedEntries();
        int deletedCount = selectedEntries.size();

        // Remove selected entries from main list
        babyCareEntries.removeAll(selectedEntries);

        // Refresh filtered list and adapter
        applyCurrentFilter();
        updateEmptyState();

        // Exit multi-select mode
        exitMultiSelectMode();

        // Save changes
        saveActivityList();

        // Show success message
        String message = getString(R.string.entries_deleted_success, deletedCount);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        Log.d("MainActivity", "Deleted " + deletedCount + " entries via multi-select");
    }

    /**
     * Handle back button press in multi-select mode
     */
    @Override
    public void onBackPressed() {
        if (isMultiSelectMode) {
            exitMultiSelectMode();
        } else {
            super.onBackPressed();
        }
    }
}
