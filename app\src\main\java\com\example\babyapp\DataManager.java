package com.example.babyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.Locale;

/**
 * Global data manager for persistent storage of baby care entries
 * Ensures data is saved and loaded consistently across all activities
 */
public class DataManager {

    private static final String TAG = "DataManager";
    private static final String PREFS_NAME = "BabyAppData";
    private static final String KEY_ACTIVITY_LIST = "activity_list";

    private static DataManager instance;
    private SharedPreferences prefs;
    private List<BabyCareEntry> activityList;
    private SimpleDateFormat dateFormat;

    /**
     * Private constructor for singleton pattern
     */
    private DataManager(Context context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        activityList = new ArrayList<>();
        loadActivityList();
    }

    /**
     * Get singleton instance of DataManager
     */
    public static synchronized DataManager getInstance(Context context) {
        if (instance == null) {
            instance = new DataManager(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * Get the current activity list
     */
    public List<BabyCareEntry> getActivityList() {
        if (activityList == null) {
            activityList = new ArrayList<>();
            loadActivityList();
        }
        return new ArrayList<>(activityList); // Return copy to prevent external modification
    }

    /**
     * Set the activity list and save to storage
     */
    public void setActivityList(List<BabyCareEntry> newList) {
        if (newList == null) {
            activityList = new ArrayList<>();
        } else {
            activityList = new ArrayList<>(newList);
        }
        saveActivityList();
        Log.d(TAG, "Activity list updated with " + activityList.size() + " entries");
    }

    /**
     * Add a new entry to the list and save
     */
    public void addEntry(BabyCareEntry entry) {
        if (entry != null) {
            activityList.add(0, entry); // Add to beginning
            saveActivityList();
            Log.d(TAG, "New entry added - total entries: " + activityList.size());
        }
    }

    /**
     * Update an existing entry and save
     */
    public void updateEntry(int position, BabyCareEntry entry) {
        if (entry != null && position >= 0 && position < activityList.size()) {
            activityList.set(position, entry);
            saveActivityList();
            Log.d(TAG, "Entry updated at position " + position);
        }
    }

    /**
     * Remove an entry and save
     */
    public void removeEntry(BabyCareEntry entry) {
        if (entry != null && activityList.remove(entry)) {
            saveActivityList();
            Log.d(TAG, "Entry removed - total entries: " + activityList.size());
        }
    }

    /**
     * Remove an entry by position and save
     */
    public void removeEntry(int position) {
        if (position >= 0 && position < activityList.size()) {
            activityList.remove(position);
            saveActivityList();
            Log.d(TAG, "Entry removed at position " + position + " - total entries: " + activityList.size());
        }
    }

    /**
     * Force save current activity list to storage
     */
    public void saveActivityList() {
        try {
            if (prefs == null) {
                Log.w(TAG, "SharedPreferences not initialized, cannot save activity list");
                return;
            }

            // Save each entry as a separate preference with index
            SharedPreferences.Editor editor = prefs.edit();

            // Clear existing entries
            int existingCount = prefs.getInt("entry_count", 0);
            for (int i = 0; i < existingCount; i++) {
                editor.remove("entry_" + i + "_time");
                editor.remove("entry_" + i + "_poop");
                editor.remove("entry_" + i + "_pee");
                editor.remove("entry_" + i + "_formula");
                editor.remove("entry_" + i + "_formula_amount");
                editor.remove("entry_" + i + "_left_breast");
                editor.remove("entry_" + i + "_right_breast");
                editor.remove("entry_" + i + "_left_time");
                editor.remove("entry_" + i + "_right_time");
                editor.remove("entry_" + i + "_burping");
                editor.remove("entry_" + i + "_description");
                editor.remove("entry_" + i + "_date_created");
            }

            // Save current entries
            editor.putInt("entry_count", activityList.size());
            for (int i = 0; i < activityList.size(); i++) {
                BabyCareEntry entry = activityList.get(i);
                String prefix = "entry_" + i + "_";

                editor.putString(prefix + "time", entry.getTime());
                editor.putBoolean(prefix + "poop", entry.isPoop());
                editor.putBoolean(prefix + "pee", entry.isPee());
                editor.putBoolean(prefix + "formula", entry.isFormula());
                editor.putInt(prefix + "formula_amount", entry.getFormulaAmount());
                editor.putBoolean(prefix + "left_breast", entry.isLeftBreast());
                editor.putBoolean(prefix + "right_breast", entry.isRightBreast());
                editor.putString(prefix + "left_time", entry.getLeftBreastTime());
                editor.putString(prefix + "right_time", entry.getRightBreastTime());
                editor.putBoolean(prefix + "burping", entry.isBurping());
                editor.putString(prefix + "description", entry.getDescription());
                editor.putString(prefix + "date_created", dateFormat.format(entry.getDateCreated()));
            }

            boolean success = editor.commit(); // Use commit for immediate write

            if (success) {
                Log.d(TAG, "Successfully saved " + activityList.size() + " entries to storage");
            } else {
                Log.e(TAG, "Failed to save activity list to storage");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error saving activity list", e);
        }
    }

    /**
     * Load activity list from storage
     */
    public void loadActivityList() {
        try {
            if (prefs == null) {
                Log.w(TAG, "SharedPreferences not initialized, cannot load activity list");
                return;
            }

            int entryCount = prefs.getInt("entry_count", 0);
            activityList.clear();

            for (int i = 0; i < entryCount; i++) {
                String prefix = "entry_" + i + "_";

                String time = prefs.getString(prefix + "time", "");
                boolean poop = prefs.getBoolean(prefix + "poop", false);
                boolean pee = prefs.getBoolean(prefix + "pee", false);
                boolean formula = prefs.getBoolean(prefix + "formula", false);
                int formulaAmount = prefs.getInt(prefix + "formula_amount", 0);
                boolean leftBreast = prefs.getBoolean(prefix + "left_breast", false);
                boolean rightBreast = prefs.getBoolean(prefix + "right_breast", false);
                String leftTime = prefs.getString(prefix + "left_time", "");
                String rightTime = prefs.getString(prefix + "right_time", "");
                boolean burping = prefs.getBoolean(prefix + "burping", false);
                String description = prefs.getString(prefix + "description", "");
                String dateCreatedStr = prefs.getString(prefix + "date_created", "");

                // Create BabyCareEntry
                BabyCareEntry entry = new BabyCareEntry(
                    time, poop, pee, formula, formulaAmount,
                    leftBreast, rightBreast, leftTime, rightTime, burping, description
                );

                // Set the date created if available
                if (!dateCreatedStr.isEmpty()) {
                    try {
                        Date dateCreated = dateFormat.parse(dateCreatedStr);
                        entry.setDateCreated(dateCreated);
                    } catch (ParseException e) {
                        Log.w(TAG, "Failed to parse date for entry " + i + ": " + dateCreatedStr);
                        // Keep the default date created by constructor
                    }
                }

                activityList.add(entry);
            }

            Log.d(TAG, "Successfully loaded " + entryCount + " entries from storage");
        } catch (Exception e) {
            Log.e(TAG, "Error loading saved activity list", e);
            // Ensure we have a valid list even if loading fails
            if (activityList == null) {
                activityList = new ArrayList<>();
            }
        }
    }

    /**
     * Get the number of entries in the activity list
     */
    public int getEntryCount() {
        return activityList != null ? activityList.size() : 0;
    }

    /**
     * Check if the activity list is empty
     */
    public boolean isEmpty() {
        return activityList == null || activityList.isEmpty();
    }

    /**
     * Clear all entries and save
     */
    public void clearAllEntries() {
        activityList.clear();
        saveActivityList();
        Log.d(TAG, "All entries cleared");
    }
}
