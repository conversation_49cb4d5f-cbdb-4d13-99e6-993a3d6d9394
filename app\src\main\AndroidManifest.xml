<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- PDF file storage permissions -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <application
        android:name=".BabyAppApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BabyApp"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.BabyApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".AddEntryActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:parentActivityName=".MainActivity"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".FilterActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".NotificationSettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:parentActivityName=".AddEntryActivity" />
        <activity
            android:name=".NotificationListActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".FullScreenNotificationActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp.FullScreen"
            android:showOnLockScreen="true"
            android:turnScreenOn="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTop" />
        <activity
            android:name=".PermissionRequestActivity"
            android:exported="false"
            android:theme="@style/Theme.BabyApp"
            android:excludeFromRecents="true"
            android:launchMode="singleTop" />

        <!-- Notification Service with enhanced background capabilities -->
        <service
            android:name=".NotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="Baby care notifications" />
        </service>

        <!-- Background Notification Service for persistent notifications -->
        <service
            android:name=".BackgroundNotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="Background baby care monitoring" />
        </service>

        <!-- Notification Receiver -->
        <receiver
            android:name=".NotificationReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.example.babyapp.NURSING_NOTIFICATION" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
