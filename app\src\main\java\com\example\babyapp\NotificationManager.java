package com.example.babyapp;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Manages nursing notifications based on user-configured intervals
 */
public class NotificationManager {

    private static final String TAG = "NotificationManager";
    private static final String PREFS_NAME = "nursing_notifications";
    private static final String KEY_NOTIFICATION_ENABLED = "notification_enabled";
    private static final String KEY_NOTIFICATION_INTERVAL = "notification_interval_minutes";
    private static final String KEY_LAST_NURSING_TIME = "last_nursing_time";
    private static final String KEY_VIBRATION_ENABLED = "vibration_enabled";
    private static final String KEY_SOUND_ENABLED = "sound_enabled";
    private static final String KEY_ACTIVE_NOTIFICATIONS = "active_notifications";
    private static final int NOTIFICATION_REQUEST_CODE = 1001;

    private Context context;
    private SharedPreferences prefs;

    public NotificationManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Save notification settings from Add Entry screen
     */
    public void saveNotificationSettings(boolean enabled, int intervalMinutes) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_NOTIFICATION_ENABLED, enabled);
        editor.putInt(KEY_NOTIFICATION_INTERVAL, intervalMinutes);
        editor.apply();

        Log.d(TAG, "Notification settings saved: enabled=" + enabled + ", interval=" + intervalMinutes + " minutes");

        if (!enabled) {
            // If disabled, cancel any existing notifications
            cancelScheduledNotification();
            cancelAllActiveNotifications();
        }
        // Note: Notification scheduling is now handled by recordEntry() method
    }

    /**
     * Record a new entry and schedule notification if enabled
     * Works with any type of entry (nursing, diaper, formula, etc.)
     * Each entry gets its own unique notification
     */
    public void recordEntry(BabyCareEntry entry) {
        // Parse the time from the main "Time" field
        long entryTime = parseEntryTime(entry);

        // Keep backward compatibility - save as last activity time
        SharedPreferences.Editor editor = prefs.edit();
        editor.putLong(KEY_LAST_NURSING_TIME, entryTime);
        editor.apply();

        // Determine entry type for logging
        String entryType = getEntryTypeDescription(entry);
        Log.d(TAG, entryType + " entry recorded at: " + new java.util.Date(entryTime) + " (timestamp: " + entryTime + ")");

        // Schedule notification if enabled - each entry gets its own notification
        if (isNotificationEnabled()) {
            scheduleNotificationForEntry(entry);
        }
    }



    /**
     * Get a description of the entry type for logging
     */
    private String getEntryTypeDescription(BabyCareEntry entry) {
        java.util.List<String> activities = new java.util.ArrayList<>();

        if (entry.isLeftBreast() || entry.isRightBreast()) {
            activities.add("Nursing");
        }
        if (entry.isPoop() || entry.isPee()) {
            activities.add("Diaper");
        }
        if (entry.isFormula()) {
            activities.add("Formula");
        }

        if (activities.isEmpty()) {
            return "General";
        } else if (activities.size() == 1) {
            return activities.get(0);
        } else {
            return String.join(" + ", activities);
        }
    }

    /**
     * Parse the time from a BabyCareEntry and convert to timestamp
     * Uses the entry's timestamp (which includes the actual date) and time field
     */
    private long parseEntryTime(BabyCareEntry entry) {
        try {
            // Use the entry's timestamp which contains the actual selected date
            Date entryTimestamp = entry.getTimestamp();
            String timeString = entry.getTime(); // This is the main "Time" field (e.g., "14:30")

            if (entryTimestamp == null) {
                Log.w(TAG, "Entry timestamp is null, using current time");
                return System.currentTimeMillis();
            }

            if (timeString == null || timeString.trim().isEmpty()) {
                Log.w(TAG, "Entry time is null or empty, using entry timestamp");
                return entryTimestamp.getTime();
            }

            // Parse the time string using TimeFormatManager to handle both formats
            TimeFormatManager timeFormatManager = new TimeFormatManager(context);
            java.text.SimpleDateFormat timeFormat = timeFormatManager.getTimeFormat();
            java.util.Date parsedTime = timeFormat.parse(timeString.trim());

            if (parsedTime == null) {
                Log.w(TAG, "Failed to parse time string: " + timeString + ", using entry timestamp");
                return entryTimestamp.getTime();
            }

            // Get the entry's date and set the time to the parsed time
            java.util.Calendar entryCalendar = java.util.Calendar.getInstance();
            entryCalendar.setTime(entryTimestamp);

            java.util.Calendar timeCalendar = java.util.Calendar.getInstance();
            timeCalendar.setTime(parsedTime);

            // Set the entry's date with the specified time
            entryCalendar.set(java.util.Calendar.HOUR_OF_DAY, timeCalendar.get(java.util.Calendar.HOUR_OF_DAY));
            entryCalendar.set(java.util.Calendar.MINUTE, timeCalendar.get(java.util.Calendar.MINUTE));
            entryCalendar.set(java.util.Calendar.SECOND, 0);
            entryCalendar.set(java.util.Calendar.MILLISECOND, 0);

            long timestamp = entryCalendar.getTimeInMillis();
            Log.d(TAG, "Parsed entry time '" + timeString + "' with date " + entryTimestamp + " to timestamp: " + timestamp + " (" + new java.util.Date(timestamp) + ")");

            return timestamp;

        } catch (Exception e) {
            Log.e(TAG, "Error parsing entry time: " + entry.getTime(), e);
            // Fallback to entry timestamp if available
            if (entry.getTimestamp() != null) {
                return entry.getTimestamp().getTime();
            }
            return System.currentTimeMillis();
        }
    }

    /**
     * Schedule notification for a specific entry
     * Each entry gets its own unique notification ID
     */
    private void scheduleNotificationForEntry(BabyCareEntry entry) {
        long entryTime = parseEntryTime(entry);
        int intervalMinutes = getNotificationInterval();

        if (entryTime <= 0 || intervalMinutes <= 0) {
            Log.w(TAG, "Cannot schedule notification: invalid entry time or interval");
            return;
        }

        // Calculate notification time
        long notificationTime = entryTime + (intervalMinutes * 60 * 1000L);
        long currentTime = System.currentTimeMillis();

        if (notificationTime <= currentTime) {
            Log.w(TAG, "Notification time is in the past, not scheduling for entry " + entry.getId());
            return;
        }

        // Use entry ID as unique notification ID
        int notificationId = (int) entry.getId();
        scheduleNotificationWithId(notificationTime, intervalMinutes, notificationId, entry);

        // Save this notification to active notifications list
        saveActiveNotification(entry.getId(), notificationTime, intervalMinutes);
    }



    /**
     * Schedule a notification with specific ID for an entry
     */
    private void scheduleNotificationWithId(long notificationTime, int intervalMinutes, int notificationId, BabyCareEntry entry) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            Log.e(TAG, "AlarmManager not available");
            return;
        }

        // Check if we can schedule exact alarms
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (!alarmManager.canScheduleExactAlarms()) {
                Log.w(TAG, "Cannot schedule exact alarms - permission not granted");
                return;
            }
        }

        Intent intent = new Intent(context, NotificationReceiver.class);
        intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
        intent.putExtra("interval_minutes", intervalMinutes);
        intent.putExtra("entry_id", entry.getId());
        intent.putExtra("notification_id", notificationId);

        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            notificationId, // Use unique ID instead of static NOTIFICATION_REQUEST_CODE
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTime, pendingIntent);
            } else {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, notificationTime, pendingIntent);
            }

            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(notificationTime);
            Log.d(TAG, "Notification scheduled for entry " + entry.getId() + " at: " + cal.getTime() +
                  " (notification ID: " + notificationId + ", interval: " + intervalMinutes + " minutes)");

        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied for exact alarm scheduling", e);
        }
    }





    /**
     * Cancel any scheduled notification
     */
    public void cancelScheduledNotification() {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            return;
        }

        Intent intent = new Intent(context, NotificationReceiver.class);
        intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");

        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            NOTIFICATION_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        alarmManager.cancel(pendingIntent);
        Log.d(TAG, "Scheduled notification cancelled");
    }

    /**
     * Cancel a specific notification by ID
     */
    public void cancelNotification(int notificationId) {
        try {
            // Handle backward compatibility for old notification system
            if (notificationId == NOTIFICATION_REQUEST_CODE) {
                cancelScheduledNotification();
                Log.d(TAG, "Cancelled legacy notification with ID " + notificationId);
                return;
            }

            // Find entry ID by notification ID in active notifications
            Map<String, ActiveNotification> activeNotifications = getActiveNotifications();
            long entryIdToRemove = -1;

            Log.d(TAG, "Looking for notification ID " + notificationId + " in " + activeNotifications.size() + " active notifications");

            for (ActiveNotification notification : activeNotifications.values()) {
                if (notification.notificationId == notificationId) {
                    entryIdToRemove = notification.entryId;
                    Log.d(TAG, "Found matching notification: entry ID " + entryIdToRemove + " has notification ID " + notificationId);
                    break;
                }
            }

            if (entryIdToRemove == -1) {
                Log.w(TAG, "No active notification found with notification ID: " + notificationId);
                return;
            }

            // Cancel the specific notification for this entry
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            if (alarmManager != null) {
                Intent intent = new Intent(context, NotificationReceiver.class);
                intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
                intent.putExtra("entry_id", entryIdToRemove);
                intent.putExtra("notification_id", notificationId);

                PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context,
                    notificationId,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );

                alarmManager.cancel(pendingIntent);
                Log.d(TAG, "Cancelled notification for entry " + entryIdToRemove + " (notification ID: " + notificationId + ")");
            }

            // Remove from active notifications list using ENTRY ID
            removeActiveNotification(entryIdToRemove);
            Log.d(TAG, "Removed notification from active list for entry " + entryIdToRemove);

        } catch (Exception e) {
            Log.e(TAG, "Error cancelling notification with ID " + notificationId, e);
        }
    }

    /**
     * Cancel notification when an entry is deleted
     * This ensures that notifications for deleted entries are properly removed
     */
    public void cancelNotificationForEntry(BabyCareEntry entry) {
        try {
            long entryId = entry.getId();
            int notificationId = (int) entryId;

            // Cancel the specific notification for this entry
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            if (alarmManager != null) {
                Intent intent = new Intent(context, NotificationReceiver.class);
                intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
                intent.putExtra("entry_id", entryId);
                intent.putExtra("notification_id", notificationId);

                PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context,
                    notificationId,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );

                alarmManager.cancel(pendingIntent);
                Log.d(TAG, "Cancelled notification for entry " + entryId + " (notification ID: " + notificationId + ")");
            }

            // Remove from active notifications list
            removeActiveNotification(entryId);

            // Backward compatibility: if this was the last activity, clear it
            long entryTime = parseEntryTime(entry);
            long lastActivityTime = getLastNursingTime();
            if (entryTime == lastActivityTime) {
                SharedPreferences.Editor editor = prefs.edit();
                editor.remove(KEY_LAST_NURSING_TIME);
                editor.apply();
                Log.d(TAG, "Cleared last activity time for backward compatibility");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error cancelling notification for entry", e);
        }
    }

    /**
     * Check if notifications are enabled
     */
    public boolean isNotificationEnabled() {
        return prefs.getBoolean(KEY_NOTIFICATION_ENABLED, false);
    }

    /**
     * Get notification interval in minutes
     */
    public int getNotificationInterval() {
        return prefs.getInt(KEY_NOTIFICATION_INTERVAL, 180); // Default 3 hours
    }

    /**
     * Get last nursing time
     */
    public long getLastNursingTime() {
        return prefs.getLong(KEY_LAST_NURSING_TIME, 0);
    }

    /**
     * Save notification interval setting only
     */
    public void saveNotificationInterval(int intervalMinutes) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_NOTIFICATION_INTERVAL, intervalMinutes);
        editor.apply();
        Log.d(TAG, "Notification interval saved: " + intervalMinutes + " minutes");
    }

    /**
     * Save vibration setting
     */
    public void saveVibrationSetting(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_VIBRATION_ENABLED, enabled);
        editor.apply();
        Log.d(TAG, "Vibration setting saved: " + enabled);
    }

    /**
     * Check if vibration is enabled
     */
    public boolean isVibrationEnabled() {
        return prefs.getBoolean(KEY_VIBRATION_ENABLED, true); // Default enabled
    }

    /**
     * Save sound setting
     */
    public void saveSoundSetting(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_SOUND_ENABLED, enabled);
        editor.apply();
        Log.d(TAG, "Sound setting saved: " + enabled);
    }

    /**
     * Check if sound is enabled
     */
    public boolean isSoundEnabled() {
        return prefs.getBoolean(KEY_SOUND_ENABLED, true); // Default enabled
    }

    /**
     * Get time until next notification (in milliseconds)
     * Returns -1 if no notification is scheduled or if time has passed
     */
    public long getTimeUntilNextNotification() {
        if (!isNotificationEnabled()) {
            return -1;
        }

        // Use active notifications instead of last nursing time
        Map<String, ActiveNotification> activeNotifications = getActiveNotifications();
        long currentTime = System.currentTimeMillis();
        long nextNotificationTime = Long.MAX_VALUE;

        for (ActiveNotification notification : activeNotifications.values()) {
            if (notification.notificationTime > currentTime) {
                nextNotificationTime = Math.min(nextNotificationTime, notification.notificationTime);
            }
        }

        return nextNotificationTime == Long.MAX_VALUE ? -1 : nextNotificationTime - currentTime;
    }

    /**
     * Test notification immediately (for debugging)
     */
    public void testNotificationNow(int intervalMinutes) {
        try {
            Log.d(TAG, "Testing full-screen notification immediately");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot test notification");
                return;
            }

            // Create test entry ID for immediate test
            long testEntryId = System.currentTimeMillis();

            Intent intent = new Intent(context, NotificationReceiver.class);
            intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
            intent.putExtra("interval_minutes", intervalMinutes);
            intent.putExtra("entry_id", testEntryId);

            // Send broadcast immediately
            context.sendBroadcast(intent);

            Log.d(TAG, "Test full-screen notification broadcast sent successfully (test ID: " + testEntryId + ")");

        } catch (Exception e) {
            Log.e(TAG, "Error testing notification", e);
        }
    }

    /**
     * Test full-screen notification directly (for immediate testing)
     */
    public void testFullScreenNotificationNow(int intervalMinutes) {
        try {
            Log.d(TAG, "Testing full-screen notification directly");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot test full-screen notification");
                return;
            }

            // Create test entry ID for direct test
            long testEntryId = System.currentTimeMillis();

            String title = "Baby Care Reminder";
            String message = createTestMessage(intervalMinutes);

            Intent fullScreenIntent = new Intent(context, FullScreenNotificationActivity.class);
            fullScreenIntent.putExtra("title", title);
            fullScreenIntent.putExtra("message", message);
            fullScreenIntent.putExtra("interval_minutes", intervalMinutes);
            fullScreenIntent.putExtra("entry_id", testEntryId);
            fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                    Intent.FLAG_ACTIVITY_CLEAR_TOP |
                                    Intent.FLAG_ACTIVITY_SINGLE_TOP);

            context.startActivity(fullScreenIntent);
            Log.d(TAG, "Test full-screen notification activity started successfully (test ID: " + testEntryId + ")");

        } catch (Exception e) {
            Log.e(TAG, "Error testing full-screen notification", e);
        }
    }

    /**
     * Create test message for notifications
     */
    private String createTestMessage(int intervalMinutes) {
        return "This is a test notification.\n\nInterval: " + formatInterval(intervalMinutes) +
               "\n\nTime to check on your baby!";
    }

    /**
     * Schedule a test notification in a few seconds
     */
    public void scheduleTestNotification(int delaySeconds, int intervalMinutes) {
        long notificationTime = System.currentTimeMillis() + (delaySeconds * 1000L);

        // Create a test entry for the notification
        long testEntryId = System.currentTimeMillis();
        int testNotificationId = (int) testEntryId;

        Log.d(TAG, "Scheduling test notification in " + delaySeconds + " seconds (test ID: " + testEntryId + ")");

        // Schedule using new system
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            Intent intent = new Intent(context, NotificationReceiver.class);
            intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
            intent.putExtra("interval_minutes", intervalMinutes);
            intent.putExtra("entry_id", testEntryId);
            intent.putExtra("notification_id", testNotificationId);

            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context,
                testNotificationId,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTime, pendingIntent);
                } else {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, notificationTime, pendingIntent);
                }

                // Save test notification to active list
                saveActiveNotification(testEntryId, notificationTime, intervalMinutes);

                Log.d(TAG, "Test notification scheduled successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error scheduling test notification", e);
            }
        }
    }

    /**
     * Snooze current notification for specified minutes
     * This creates a new notification that will trigger after the snooze period
     */
    public void snoozeNotification(int snoozeMinutes) {
        try {
            // For backward compatibility, also cancel legacy notification
            cancelScheduledNotification();

            // Calculate snooze time
            long currentTime = System.currentTimeMillis();
            long snoozeDelay = snoozeMinutes * 60 * 1000L;
            long snoozeTime = currentTime + snoozeDelay;

            // Create a temporary entry for snooze notification
            // Use current time as entry ID to make it unique
            long snoozeEntryId = currentTime;
            int snoozeNotificationId = (int) snoozeEntryId;

            // Schedule snooze notification
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            if (alarmManager != null) {
                Intent intent = new Intent(context, NotificationReceiver.class);
                intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
                intent.putExtra("interval_minutes", getNotificationInterval());
                intent.putExtra("entry_id", snoozeEntryId);
                intent.putExtra("notification_id", snoozeNotificationId);

                PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context,
                    snoozeNotificationId,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, snoozeTime, pendingIntent);
                } else {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, snoozeTime, pendingIntent);
                }

                // Save snooze notification to active list
                saveActiveNotification(snoozeEntryId, snoozeTime, getNotificationInterval());

                Log.d(TAG, "Notification snoozed for " + snoozeMinutes + " minutes (snooze ID: " + snoozeEntryId + ")");
                Log.d(TAG, "Next notification will trigger at: " + new java.util.Date(snoozeTime));
            }

        } catch (Exception e) {
            Log.e(TAG, "Error snoozing notification", e);
        }
    }

    /**
     * Get human-readable status of notification system with seconds precision
     */
    public String getNotificationStatus() {
        try {
            if (!isNotificationEnabled()) {
                return context.getString(R.string.status_notifications_disabled);
            }

            long timeUntil = getTimeUntilNextNotification();
            if (timeUntil <= 0) {
                return context.getString(R.string.status_no_upcoming);
            }

            int hours = (int) (timeUntil / (1000 * 60 * 60));
            int minutes = (int) ((timeUntil % (1000 * 60 * 60)) / (1000 * 60));
            int seconds = (int) ((timeUntil % (1000 * 60)) / 1000);

            // Format based on time remaining
            if (hours > 0) {
                // More than 1 hour: show hours and minutes only
                return context.getString(R.string.status_next_hours_minutes, hours, minutes);
            } else if (minutes > 0) {
                // Less than 1 hour but more than 1 minute: show minutes and seconds
                return context.getString(R.string.status_next_minutes_seconds, minutes, seconds);
            } else {
                // Less than 1 minute: show seconds only
                return context.getString(R.string.status_next_seconds, seconds);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting notification status", e);
            return context.getString(R.string.status_error);
        }
    }

    /**
     * Save active notification to SharedPreferences
     */
    private void saveActiveNotification(long entryId, long notificationTime, int intervalMinutes) {
        try {
            Map<String, ActiveNotification> activeNotifications = getActiveNotifications();

            ActiveNotification notification = new ActiveNotification();
            notification.entryId = entryId;
            notification.notificationTime = notificationTime;
            notification.intervalMinutes = intervalMinutes;
            notification.notificationId = (int) entryId;

            activeNotifications.put(String.valueOf(entryId), notification);

            Gson gson = new Gson();
            String json = gson.toJson(activeNotifications);

            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_ACTIVE_NOTIFICATIONS, json);
            editor.apply();

            Log.d(TAG, "Active notification saved for entry " + entryId);
        } catch (Exception e) {
            Log.e(TAG, "Error saving active notification", e);
        }
    }

    /**
     * Get all active notifications from SharedPreferences
     */
    private Map<String, ActiveNotification> getActiveNotifications() {
        try {
            String json = prefs.getString(KEY_ACTIVE_NOTIFICATIONS, "{}");
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, ActiveNotification>>(){}.getType();
            Map<String, ActiveNotification> notifications = gson.fromJson(json, type);
            return notifications != null ? notifications : new HashMap<>();
        } catch (Exception e) {
            Log.e(TAG, "Error getting active notifications", e);
            return new HashMap<>();
        }
    }

    /**
     * Remove active notification when entry is deleted
     */
    private void removeActiveNotification(long entryId) {
        try {
            Map<String, ActiveNotification> activeNotifications = getActiveNotifications();

            // Debug: Log before removal
            Log.d(TAG, "Before removal - Active notifications count: " + activeNotifications.size());
            for (String key : activeNotifications.keySet()) {
                Log.d(TAG, "  - Entry ID: " + key + ", Notification ID: " + activeNotifications.get(key).notificationId);
            }

            boolean wasRemoved = activeNotifications.remove(String.valueOf(entryId)) != null;
            Log.d(TAG, "Attempting to remove entry " + entryId + " - was present: " + wasRemoved);

            Gson gson = new Gson();
            String json = gson.toJson(activeNotifications);

            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_ACTIVE_NOTIFICATIONS, json);
            editor.commit(); // Use commit() instead of apply() for immediate persistence

            // Debug: Log after removal
            Log.d(TAG, "After removal - Active notifications count: " + activeNotifications.size());
            Log.d(TAG, "Active notification removed for entry " + entryId + " (was present: " + wasRemoved + ")");
        } catch (Exception e) {
            Log.e(TAG, "Error removing active notification", e);
        }
    }

    /**
     * Cancel all active notifications (used when notifications are disabled)
     */
    private void cancelAllActiveNotifications() {
        try {
            Map<String, ActiveNotification> activeNotifications = getActiveNotifications();
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

            if (alarmManager != null) {
                for (ActiveNotification notification : activeNotifications.values()) {
                    Intent intent = new Intent(context, NotificationReceiver.class);
                    intent.setAction("com.example.babyapp.NURSING_NOTIFICATION");
                    intent.putExtra("entry_id", notification.entryId);
                    intent.putExtra("notification_id", notification.notificationId);

                    PendingIntent pendingIntent = PendingIntent.getBroadcast(
                        context,
                        notification.notificationId,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                    );

                    alarmManager.cancel(pendingIntent);
                    Log.d(TAG, "Cancelled active notification for entry " + notification.entryId);
                }
            }

            // Clear all active notifications from storage
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_ACTIVE_NOTIFICATIONS, "{}");
            editor.apply();

            Log.d(TAG, "All active notifications cancelled and cleared");
        } catch (Exception e) {
            Log.e(TAG, "Error cancelling all active notifications", e);
        }
    }

    /**
     * Get list of scheduled notifications for display
     */
    public List<NotificationInfo> getScheduledNotifications() {
        List<NotificationInfo> notifications = new ArrayList<>();

        try {
            if (!isNotificationEnabled()) {
                return notifications; // Empty list if notifications disabled
            }

            // Get all active notifications
            Map<String, ActiveNotification> activeNotifications = getActiveNotifications();
            long currentTime = System.currentTimeMillis();

            for (ActiveNotification activeNotification : activeNotifications.values()) {
                if (activeNotification.notificationTime > currentTime) {
                    // This notification is still in the future
                    TimeFormatManager timeFormatManager = new TimeFormatManager(context);
                    SimpleDateFormat dateFormat = timeFormatManager.getDateTimeFormat();

                    String scheduledTimeStr = dateFormat.format(new Date(activeNotification.notificationTime));

                    String title = "Baby Care Reminder";
                    String description = "Reminder after " + formatInterval(activeNotification.intervalMinutes);
                    String details = "Entry ID: " + activeNotification.entryId + "\nScheduled: " + scheduledTimeStr;

                    NotificationInfo info = new NotificationInfo(
                        activeNotification.notificationId,
                        title,
                        description,
                        details,
                        scheduledTimeStr,
                        activeNotification.notificationTime,
                        true
                    );
                    notifications.add(info);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error getting scheduled notifications", e);
        }

        return notifications;
    }

    /**
     * Format interval minutes into human-readable string
     */
    private String formatInterval(int intervalMinutes) {
        if (intervalMinutes < 60) {
            return intervalMinutes + " minutes";
        } else {
            int hours = intervalMinutes / 60;
            int minutes = intervalMinutes % 60;

            if (minutes == 0) {
                return hours == 1 ? "1 hour" : hours + " hours";
            } else {
                return hours + "h " + minutes + "m";
            }
        }
    }

    /**
     * Class to hold active notification data for storage
     */
    public static class ActiveNotification {
        public long entryId;
        public long notificationTime;
        public int intervalMinutes;
        public int notificationId;
    }

    /**
     * Class to hold notification information for display
     */
    public static class NotificationInfo {
        public int id;
        public String title;
        public String description;
        public String details;
        public String scheduledTime;
        public long scheduledTimeMillis;
        public boolean isActive;

        public NotificationInfo(int id, String title, String description, String details, String scheduledTime, long scheduledTimeMillis, boolean isActive) {
            this.id = id;
            this.title = title;
            this.description = description;
            this.details = details;
            this.scheduledTime = scheduledTime;
            this.scheduledTimeMillis = scheduledTimeMillis;
            this.isActive = isActive;
        }
    }
}
