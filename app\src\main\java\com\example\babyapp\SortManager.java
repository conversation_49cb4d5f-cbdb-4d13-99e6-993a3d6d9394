package com.example.babyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Manages sorting functionality for baby care entries
 */
public class SortManager {
    private static final String TAG = "SortManager";
    private static final String PREFS_NAME = "SortPreferences";
    private static final String PREF_SORT_TYPE = "sort_type";
    private static final String PREF_SORT_DIRECTION = "sort_direction";
    
    // Sort types
    public enum SortType {
        DATE, TIME
    }
    
    // Sort directions
    public enum SortDirection {
        ASCENDING, DESCENDING
    }
    
    private Context context;
    private SharedPreferences preferences;
    private SortType currentSortType = SortType.DATE;
    private SortDirection currentSortDirection = SortDirection.DESCENDING; // Newest first by default
    
    public SortManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        loadSortPreferences();
    }
    
    /**
     * Load saved sort preferences
     */
    private void loadSortPreferences() {
        try {
            String sortTypeStr = preferences.getString(PREF_SORT_TYPE, SortType.DATE.name());
            String sortDirectionStr = preferences.getString(PREF_SORT_DIRECTION, SortDirection.DESCENDING.name());
            
            currentSortType = SortType.valueOf(sortTypeStr);
            currentSortDirection = SortDirection.valueOf(sortDirectionStr);
            
            Log.d(TAG, "Loaded sort preferences: " + currentSortType + " " + currentSortDirection);
        } catch (Exception e) {
            Log.e(TAG, "Error loading sort preferences, using defaults", e);
            currentSortType = SortType.DATE;
            currentSortDirection = SortDirection.DESCENDING;
        }
    }
    
    /**
     * Save sort preferences
     */
    private void saveSortPreferences() {
        try {
            SharedPreferences.Editor editor = preferences.edit();
            editor.putString(PREF_SORT_TYPE, currentSortType.name());
            editor.putString(PREF_SORT_DIRECTION, currentSortDirection.name());
            editor.apply();
            
            Log.d(TAG, "Saved sort preferences: " + currentSortType + " " + currentSortDirection);
        } catch (Exception e) {
            Log.e(TAG, "Error saving sort preferences", e);
        }
    }
    
    /**
     * Sort entries by date (includes both date and time from timestamp)
     */
    public List<BabyCareEntry> sortByDate(List<BabyCareEntry> entries, SortDirection direction) {
        currentSortType = SortType.DATE;
        currentSortDirection = direction;
        saveSortPreferences();

        List<BabyCareEntry> sortedEntries = new ArrayList<>(entries);

        Collections.sort(sortedEntries, new Comparator<BabyCareEntry>() {
            @Override
            public int compare(BabyCareEntry entry1, BabyCareEntry entry2) {
                Date date1 = entry1.getTimestamp();
                Date date2 = entry2.getTimestamp();

                if (date1 == null && date2 == null) return 0;
                if (date1 == null) return 1;
                if (date2 == null) return -1;

                // Compare full timestamp (date + time)
                int result = date1.compareTo(date2);
                return direction == SortDirection.ASCENDING ? result : -result;
            }
        });

        Log.d(TAG, "Sorted " + entries.size() + " entries by date+time " + direction);
        return sortedEntries;
    }
    
    /**
     * Sort entries by time (time field first, then date if times are equal)
     */
    public List<BabyCareEntry> sortByTime(List<BabyCareEntry> entries, SortDirection direction) {
        currentSortType = SortType.TIME;
        currentSortDirection = direction;
        saveSortPreferences();

        List<BabyCareEntry> sortedEntries = new ArrayList<>(entries);

        Collections.sort(sortedEntries, new Comparator<BabyCareEntry>() {
            @Override
            public int compare(BabyCareEntry entry1, BabyCareEntry entry2) {
                String time1 = entry1.getTime();
                String time2 = entry2.getTime();

                if (time1 == null && time2 == null) {
                    // If both times are null, compare by date
                    return compareDates(entry1, entry2, direction);
                }
                if (time1 == null) return 1;
                if (time2 == null) return -1;

                // Parse time strings to compare
                Date timeDate1 = parseTimeString(time1);
                Date timeDate2 = parseTimeString(time2);

                if (timeDate1 == null && timeDate2 == null) {
                    // If both times can't be parsed, compare by date
                    return compareDates(entry1, entry2, direction);
                }
                if (timeDate1 == null) return 1;
                if (timeDate2 == null) return -1;

                // Compare times first
                int timeResult = timeDate1.compareTo(timeDate2);
                if (timeResult != 0) {
                    return direction == SortDirection.ASCENDING ? timeResult : -timeResult;
                }

                // If times are equal, compare by date as secondary sort
                return compareDates(entry1, entry2, direction);
            }
        });

        Log.d(TAG, "Sorted " + entries.size() + " entries by time " + direction);
        return sortedEntries;
    }

    /**
     * Helper method to compare entries by date
     */
    private int compareDates(BabyCareEntry entry1, BabyCareEntry entry2, SortDirection direction) {
        Date date1 = entry1.getTimestamp();
        Date date2 = entry2.getTimestamp();

        if (date1 == null && date2 == null) return 0;
        if (date1 == null) return 1;
        if (date2 == null) return -1;

        int result = date1.compareTo(date2);
        return direction == SortDirection.ASCENDING ? result : -result;
    }
    
    /**
     * Parse time string to Date for comparison
     */
    private Date parseTimeString(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        
        // Try different time formats
        String[] timeFormats = {
            "HH:mm",     // 24-hour format
            "h:mm a",    // 12-hour format with AM/PM
            "hh:mm a",   // 12-hour format with leading zero
            "H:mm",      // 24-hour format without leading zero
        };
        
        for (String format : timeFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
                return sdf.parse(timeStr.trim());
            } catch (ParseException e) {
                // Try next format
            }
        }
        
        Log.w(TAG, "Could not parse time string: " + timeStr);
        return null;
    }
    
    // Getters
    public SortType getCurrentSortType() {
        return currentSortType;
    }
    
    public SortDirection getCurrentSortDirection() {
        return currentSortDirection;
    }
    
    /**
     * Toggle sort direction for current sort type
     */
    public SortDirection toggleDirection() {
        currentSortDirection = (currentSortDirection == SortDirection.ASCENDING) ? 
            SortDirection.DESCENDING : SortDirection.ASCENDING;
        saveSortPreferences();
        return currentSortDirection;
    }
}
