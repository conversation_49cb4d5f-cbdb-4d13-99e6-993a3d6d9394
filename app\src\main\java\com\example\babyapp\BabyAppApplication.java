package com.example.babyapp;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Custom Application class to handle app-wide lifecycle events
 * Ensures data is saved when app is closed from any screen
 */
public class BabyAppApplication extends Application implements Application.ActivityLifecycleCallbacks {
    
    private static final String TAG = "BabyAppApplication";
    private int activityCount = 0;
    private DataManager dataManager;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Application created");

        // Initialize locale management
        String savedLanguage = LocaleManager.getSavedLanguage(this);
        LocaleManager.setLocale(this, savedLanguage);
        Log.d(TAG, "Application locale set to: " + savedLanguage);

        // Initialize DataManager
        dataManager = DataManager.getInstance(this);

        // Register activity lifecycle callbacks
        registerActivityLifecycleCallbacks(this);
    }

    @Override
    protected void attachBaseContext(Context base) {
        // Apply saved language before application creation
        String savedLanguage = LocaleManager.getSavedLanguage(base);
        Context updatedContext = LocaleManager.updateBaseContextLocale(base, savedLanguage);
        super.attachBaseContext(updatedContext);

        Log.d(TAG, "Application attachBaseContext - Language: " + savedLanguage);
    }
    
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "Activity created: " + activity.getClass().getSimpleName());
    }
    
    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        activityCount++;
        Log.d(TAG, "Activity started: " + activity.getClass().getSimpleName() + " (count: " + activityCount + ")");
    }
    
    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        Log.d(TAG, "Activity resumed: " + activity.getClass().getSimpleName());
    }
    
    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        Log.d(TAG, "Activity paused: " + activity.getClass().getSimpleName());
        
        // Save data when any activity is paused
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
    
    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        activityCount--;
        Log.d(TAG, "Activity stopped: " + activity.getClass().getSimpleName() + " (count: " + activityCount + ")");
        
        // Save data when any activity is stopped
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
        
        // If no activities are running, the app is going to background
        if (activityCount == 0) {
            Log.d(TAG, "App going to background - saving data");
            if (dataManager != null) {
                dataManager.saveActivityList();
            }
        }
    }
    
    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        Log.d(TAG, "Activity saving instance state: " + activity.getClass().getSimpleName());
        
        // Save data when instance state is saved
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
    
    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        Log.d(TAG, "Activity destroyed: " + activity.getClass().getSimpleName());
        
        // Save data when any activity is destroyed
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        Log.d(TAG, "Application terminating - saving data");
        
        // Save data when application is terminating
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
    
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Log.d(TAG, "Low memory - saving data");
        
        // Save data when system is low on memory
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
    
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.d(TAG, "Trim memory (level: " + level + ") - saving data");
        
        // Save data when system requests memory trimming
        if (dataManager != null) {
            dataManager.saveActivityList();
        }
    }
}
