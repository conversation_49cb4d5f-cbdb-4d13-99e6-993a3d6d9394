package com.example.babyapp;

import java.io.Serializable;
import java.util.Date;

/**
 * Class to hold filter criteria for baby care entries
 */
public class FilterCriteria implements Serializable {

    // Date filter options
    public static final String DATE_TODAY = "Today";
    public static final String DATE_THIS_WEEK = "This Week";
    public static final String DATE_THIS_MONTH = "This Month";
    public static final String DATE_CUSTOM_RANGE = "Custom Range";

    // Diaper filter options
    public static final String DIAPER_POOP_ONLY = "Poop Only";
    public static final String DIAPER_PEE_ONLY = "Pee Only";
    public static final String DIAPER_ANY = "Any Diaper Change";

    // Formula filter options
    public static final String FORMULA_ANY = "Any Amount";
    public static final String FORMULA_LESS_100 = "Less than 100ml";
    public static final String FORMULA_MORE_200 = "More than 200ml";

    // Selected filters
    private String selectedDateFilter;
    private String selectedDiaperFilter;
    private String selectedFormulaFilter;

    // Custom date range fields
    private Date customFromDate;
    private Date customToDate;
    private boolean isDateRange = true; // true for range, false for exact date

    // Custom formula amount
    private Integer customFormulaAmount;

    public FilterCriteria() {
        // Initialize with no filters selected
        this.selectedDateFilter = null;
        this.selectedDiaperFilter = null;
        this.selectedFormulaFilter = null;
        this.customFromDate = null;
        this.customToDate = null;
        this.isDateRange = true;
        this.customFormulaAmount = null;
    }

    // Getters
    public String getSelectedDateFilter() { return selectedDateFilter; }
    public String getSelectedDiaperFilter() { return selectedDiaperFilter; }
    public String getSelectedFormulaFilter() { return selectedFormulaFilter; }
    public Date getCustomFromDate() { return customFromDate; }
    public Date getCustomToDate() { return customToDate; }
    public boolean isDateRange() { return isDateRange; }
    public Integer getCustomFormulaAmount() { return customFormulaAmount; }

    // Setters
    public void setSelectedDateFilter(String filter) { this.selectedDateFilter = filter; }
    public void setSelectedDiaperFilter(String filter) { this.selectedDiaperFilter = filter; }
    public void setSelectedFormulaFilter(String filter) { this.selectedFormulaFilter = filter; }
    public void setCustomFromDate(Date date) { this.customFromDate = date; }
    public void setCustomToDate(Date date) { this.customToDate = date; }
    public void setIsDateRange(boolean isDateRange) { this.isDateRange = isDateRange; }
    public void setCustomFormulaAmount(Integer amount) { this.customFormulaAmount = amount; }

    // Check if any filters are applied
    public boolean hasFilters() {
        return selectedDateFilter != null || selectedDiaperFilter != null || selectedFormulaFilter != null;
    }

    // Get filter description for display
    public String getFilterDescription() {
        StringBuilder description = new StringBuilder();

        if (selectedDateFilter != null) {
            description.append(selectedDateFilter);
        }

        if (selectedDiaperFilter != null) {
            if (description.length() > 0) description.append(" • ");
            description.append(selectedDiaperFilter);
        }

        if (selectedFormulaFilter != null) {
            if (description.length() > 0) description.append(" • ");
            description.append(selectedFormulaFilter);
        }

        return description.toString();
    }

    // Get individual filter descriptions for removable chips
    public java.util.List<String> getIndividualFilters() {
        java.util.List<String> filters = new java.util.ArrayList<>();

        if (selectedDateFilter != null) {
            filters.add(selectedDateFilter);
        }

        if (selectedDiaperFilter != null) {
            filters.add(selectedDiaperFilter);
        }

        if (selectedFormulaFilter != null) {
            filters.add(selectedFormulaFilter);
        }

        return filters;
    }

    // Remove specific filter by name
    public void removeFilter(String filterName) {
        if (filterName.equals(selectedDateFilter)) {
            selectedDateFilter = null;
        } else if (filterName.equals(selectedDiaperFilter)) {
            selectedDiaperFilter = null;
        } else if (filterName.equals(selectedFormulaFilter)) {
            selectedFormulaFilter = null;
        }
    }

    // Clear all filters
    public void clearAllFilters() {
        selectedDateFilter = null;
        selectedDiaperFilter = null;
        selectedFormulaFilter = null;
    }
}
