package com.example.babyapp;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Filter Activity for filtering baby care entries
 * Features expandable/collapsible filter sections for Date, Diaper, and Formula
 */
public class FilterActivity extends BaseActivity {

    // UI Components
    private ImageView backButton;

    // Date Filter
    private LinearLayout dateFilterHeader;
    private LinearLayout dateFilterContent;
    private ImageView dateExpandIcon;
    private boolean isDateExpanded = false;

    // Diaper Filter
    private LinearLayout diaperFilterHeader;
    private LinearLayout diaperFilterContent;
    private ImageView diaperExpandIcon;
    private boolean isDiaperExpanded = false;

    // Formula Filter
    private LinearLayout formulaFilterHeader;
    private LinearLayout formulaFilterContent;
    private ImageView formulaExpandIcon;
    private boolean isFormulaExpanded = false;

    // Filter Criteria
    private FilterCriteria filterCriteria;

    // Date Filter Options
    private TextView dateToday, dateThisWeek, dateThisMonth, dateCustomRange;

    // Diaper Filter Options
    private TextView diaperPoopOnly, diaperPeeOnly, diaperBoth, diaperAny;

    // Formula Filter Options
    private TextView formulaAny, formulaLess100, formula100to200, formulaMore200;

    // Action Button
    private android.widget.Button applyFiltersButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_filter);

        // Initialize filter criteria - get existing filters if passed
        Intent intent = getIntent();
        if (intent.hasExtra("current_filter")) {
            filterCriteria = (FilterCriteria) intent.getSerializableExtra("current_filter");
        } else {
            filterCriteria = new FilterCriteria();
        }

        initializeComponents();
        setupClickListeners();
        updateFilterSelectionUI();
    }

    /**
     * Initialize all UI components
     */
    private void initializeComponents() {
        // Back button
        backButton = findViewById(R.id.backButton);

        // Date filter components
        dateFilterHeader = findViewById(R.id.dateFilterHeader);
        dateFilterContent = findViewById(R.id.dateFilterContent);
        dateExpandIcon = findViewById(R.id.dateExpandIcon);

        // Diaper filter components
        diaperFilterHeader = findViewById(R.id.diaperFilterHeader);
        diaperFilterContent = findViewById(R.id.diaperFilterContent);
        diaperExpandIcon = findViewById(R.id.diaperExpandIcon);

        // Formula filter components
        formulaFilterHeader = findViewById(R.id.formulaFilterHeader);
        formulaFilterContent = findViewById(R.id.formulaFilterContent);
        formulaExpandIcon = findViewById(R.id.formulaExpandIcon);

        // Date filter options
        dateToday = findViewById(R.id.dateToday);
        dateThisWeek = findViewById(R.id.dateThisWeek);
        dateThisMonth = findViewById(R.id.dateThisMonth);
        dateCustomRange = findViewById(R.id.dateCustomRange);

        // Diaper filter options
        diaperPoopOnly = findViewById(R.id.diaperPoopOnly);
        diaperPeeOnly = findViewById(R.id.diaperPeeOnly);
        diaperBoth = findViewById(R.id.diaperBoth);
        diaperAny = findViewById(R.id.diaperAny);

        // Formula filter options
        formulaAny = findViewById(R.id.formulaAny);
        formulaLess100 = findViewById(R.id.formulaLess100);
        formula100to200 = findViewById(R.id.formula100to200);
        formulaMore200 = findViewById(R.id.formulaMore200);

        // Action button
        applyFiltersButton = findViewById(R.id.applyFiltersButton);
    }

    /**
     * Set up click listeners for all interactive elements
     */
    private void setupClickListeners() {
        // Back button
        backButton.setOnClickListener(v -> finish());

        // Date filter expand/collapse
        dateFilterHeader.setOnClickListener(v -> toggleDateFilter());

        // Diaper filter expand/collapse
        diaperFilterHeader.setOnClickListener(v -> toggleDiaperFilter());

        // Formula filter expand/collapse
        formulaFilterHeader.setOnClickListener(v -> toggleFormulaFilter());

        // Date filter options
        dateToday.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_TODAY));
        dateThisWeek.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_THIS_WEEK));
        dateThisMonth.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_THIS_MONTH));
        dateCustomRange.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_CUSTOM_RANGE));

        // Diaper filter options
        diaperPoopOnly.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_POOP_ONLY));
        diaperPeeOnly.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_PEE_ONLY));
        diaperBoth.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_BOTH));
        diaperAny.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_ANY));

        // Formula filter options
        formulaAny.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_ANY));
        formulaLess100.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_LESS_100));
        formula100to200.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_100_TO_200));
        formulaMore200.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_MORE_200));

        // Action button
        applyFiltersButton.setOnClickListener(v -> applyFilters());
    }

    /**
     * Toggle Date filter section expand/collapse
     */
    private void toggleDateFilter() {
        if (isDateExpanded) {
            // Collapse
            dateFilterContent.setVisibility(View.GONE);
            dateExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isDateExpanded = false;
        } else {
            // Expand
            dateFilterContent.setVisibility(View.VISIBLE);
            dateExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isDateExpanded = true;
        }
    }

    /**
     * Toggle Diaper filter section expand/collapse
     */
    private void toggleDiaperFilter() {
        if (isDiaperExpanded) {
            // Collapse
            diaperFilterContent.setVisibility(View.GONE);
            diaperExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isDiaperExpanded = false;
        } else {
            // Expand
            diaperFilterContent.setVisibility(View.VISIBLE);
            diaperExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isDiaperExpanded = true;
        }
    }

    /**
     * Toggle Formula filter section expand/collapse
     */
    private void toggleFormulaFilter() {
        if (isFormulaExpanded) {
            // Collapse
            formulaFilterContent.setVisibility(View.GONE);
            formulaExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isFormulaExpanded = false;
        } else {
            // Expand
            formulaFilterContent.setVisibility(View.VISIBLE);
            formulaExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isFormulaExpanded = true;
        }
    }

    /**
     * Select date filter option
     */
    private void selectDateFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedDateFilter())) {
            filterCriteria.setSelectedDateFilter(null);
        } else {
            filterCriteria.setSelectedDateFilter(filter);
        }
        updateDateFilterUI();
    }

    /**
     * Select diaper filter option
     */
    private void selectDiaperFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedDiaperFilter())) {
            filterCriteria.setSelectedDiaperFilter(null);
        } else {
            filterCriteria.setSelectedDiaperFilter(filter);
        }
        updateDiaperFilterUI();
    }

    /**
     * Select formula filter option
     */
    private void selectFormulaFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedFormulaFilter())) {
            filterCriteria.setSelectedFormulaFilter(null);
        } else {
            filterCriteria.setSelectedFormulaFilter(filter);
        }
        updateFormulaFilterUI();
    }

    /**
     * Apply selected filters and return to main activity
     */
    private void applyFilters() {
        // Always return filter criteria, even if empty (to allow clearing filters)
        Intent resultIntent = new Intent();
        resultIntent.putExtra("filter_criteria", filterCriteria);
        setResult(RESULT_OK, resultIntent);


        finish();
    }

    /**
     * Update UI to show currently selected filters
     */
    private void updateFilterSelectionUI() {
        // Update date filter options
        updateDateFilterUI();
        updateDiaperFilterUI();
        updateFormulaFilterUI();
    }

    /**
     * Update date filter UI to show current selection
     */
    private void updateDateFilterUI() {
        // Reset all date options
        resetTextViewStyle(dateToday);
        resetTextViewStyle(dateThisWeek);
        resetTextViewStyle(dateThisMonth);
        resetTextViewStyle(dateCustomRange);

        // Highlight selected option
        String selectedDate = filterCriteria.getSelectedDateFilter();
        if (selectedDate != null) {
            switch (selectedDate) {
                case FilterCriteria.DATE_TODAY:
                    highlightTextView(dateToday);
                    break;
                case FilterCriteria.DATE_THIS_WEEK:
                    highlightTextView(dateThisWeek);
                    break;
                case FilterCriteria.DATE_THIS_MONTH:
                    highlightTextView(dateThisMonth);
                    break;
                case FilterCriteria.DATE_CUSTOM_RANGE:
                    highlightTextView(dateCustomRange);
                    break;
            }
        }
    }

    /**
     * Update diaper filter UI to show current selection
     */
    private void updateDiaperFilterUI() {
        // Reset all diaper options
        resetTextViewStyle(diaperPoopOnly);
        resetTextViewStyle(diaperPeeOnly);
        resetTextViewStyle(diaperBoth);
        resetTextViewStyle(diaperAny);

        // Highlight selected option
        String selectedDiaper = filterCriteria.getSelectedDiaperFilter();
        if (selectedDiaper != null) {
            switch (selectedDiaper) {
                case FilterCriteria.DIAPER_POOP_ONLY:
                    highlightTextView(diaperPoopOnly);
                    break;
                case FilterCriteria.DIAPER_PEE_ONLY:
                    highlightTextView(diaperPeeOnly);
                    break;
                case FilterCriteria.DIAPER_BOTH:
                    highlightTextView(diaperBoth);
                    break;
                case FilterCriteria.DIAPER_ANY:
                    highlightTextView(diaperAny);
                    break;
            }
        }
    }

    /**
     * Update formula filter UI to show current selection
     */
    private void updateFormulaFilterUI() {
        // Reset all formula options
        resetTextViewStyle(formulaAny);
        resetTextViewStyle(formulaLess100);
        resetTextViewStyle(formula100to200);
        resetTextViewStyle(formulaMore200);

        // Highlight selected option
        String selectedFormula = filterCriteria.getSelectedFormulaFilter();
        if (selectedFormula != null) {
            switch (selectedFormula) {
                case FilterCriteria.FORMULA_ANY:
                    highlightTextView(formulaAny);
                    break;
                case FilterCriteria.FORMULA_LESS_100:
                    highlightTextView(formulaLess100);
                    break;
                case FilterCriteria.FORMULA_100_TO_200:
                    highlightTextView(formula100to200);
                    break;
                case FilterCriteria.FORMULA_MORE_200:
                    highlightTextView(formulaMore200);
                    break;
            }
        }
    }

    /**
     * Highlight a TextView to show it's selected
     */
    private void highlightTextView(TextView textView) {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                textView.setBackgroundColor(getResources().getColor(R.color.baby_pink, null));
                textView.setTextColor(getResources().getColor(R.color.white, null));
            } else {
                textView.setBackgroundColor(getResources().getColor(R.color.baby_pink));
                textView.setTextColor(getResources().getColor(R.color.white));
            }
        } catch (Exception e) {
            // Fallback to simple highlighting
            textView.setBackgroundColor(0xFFE91E63); // Pink color
            textView.setTextColor(0xFFFFFFFF); // White color
        }
    }

    /**
     * Reset TextView style to default
     */
    private void resetTextViewStyle(TextView textView) {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                textView.setBackground(getResources().getDrawable(android.R.attr.selectableItemBackground, null));
                textView.setTextColor(getResources().getColor(R.color.dark_blue, null));
            } else {
                textView.setBackgroundResource(android.R.attr.selectableItemBackground);
                textView.setTextColor(getResources().getColor(R.color.dark_blue));
            }
        } catch (Exception e) {
            // Fallback to simple reset
            textView.setBackgroundColor(0x00000000); // Transparent
            textView.setTextColor(0xFF1976D2); // Dark blue color
        }
    }
}
