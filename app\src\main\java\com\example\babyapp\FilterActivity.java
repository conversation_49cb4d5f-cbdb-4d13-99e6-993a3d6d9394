package com.example.babyapp;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * Filter Activity for filtering baby care entries
 * Features expandable/collapsible filter sections for Date, Diaper, and Formula
 */
public class FilterActivity extends BaseActivity {

    // UI Components
    private ImageView backButton;

    // Date Filter
    private LinearLayout dateFilterHeader;
    private LinearLayout dateFilterContent;
    private ImageView dateExpandIcon;
    private boolean isDateExpanded = false;

    // Diaper Filter
    private LinearLayout diaperFilterHeader;
    private LinearLayout diaperFilterContent;
    private ImageView diaperExpandIcon;
    private boolean isDiaperExpanded = false;

    // Formula Filter
    private LinearLayout formulaFilterHeader;
    private LinearLayout formulaFilterContent;
    private ImageView formulaExpandIcon;
    private boolean isFormulaExpanded = false;

    // Filter Criteria
    private FilterCriteria filterCriteria;

    // Date Filter Options
    private TextView dateToday, dateThisWeek, dateThisMonth, dateCustomRange;

    // Custom Range Options
    private LinearLayout customRangeOptions;
    private Button fromDateButton, toDateButton;
    private Date selectedFromDate, selectedToDate;
    private Date minAllowedDate, maxAllowedDate;

    // Diaper Filter Options
    private TextView diaperPoopOnly, diaperPeeOnly, diaperAny;

    // Formula Filter Options
    private TextView formulaAny, formulaLess100, formulaMore200;

    // Custom Formula Amount Options
    private LinearLayout customAmountOptions;
    private EditText customAmountInput;

    // Action Button
    private android.widget.Button applyFiltersButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_filter);

        // Initialize filter criteria - get existing filters if passed
        Intent intent = getIntent();
        if (intent.hasExtra("current_filter")) {
            filterCriteria = (FilterCriteria) intent.getSerializableExtra("current_filter");
        } else {
            filterCriteria = new FilterCriteria();
        }

        initializeComponents();
        setupClickListeners();
        updateFilterSelectionUI();
    }

    /**
     * Initialize all UI components
     */
    private void initializeComponents() {
        // Back button
        backButton = findViewById(R.id.backButton);

        // Date filter components
        dateFilterHeader = findViewById(R.id.dateFilterHeader);
        dateFilterContent = findViewById(R.id.dateFilterContent);
        dateExpandIcon = findViewById(R.id.dateExpandIcon);

        // Diaper filter components
        diaperFilterHeader = findViewById(R.id.diaperFilterHeader);
        diaperFilterContent = findViewById(R.id.diaperFilterContent);
        diaperExpandIcon = findViewById(R.id.diaperExpandIcon);

        // Formula filter components
        formulaFilterHeader = findViewById(R.id.formulaFilterHeader);
        formulaFilterContent = findViewById(R.id.formulaFilterContent);
        formulaExpandIcon = findViewById(R.id.formulaExpandIcon);

        // Date filter options
        dateToday = findViewById(R.id.dateToday);
        dateThisWeek = findViewById(R.id.dateThisWeek);
        dateThisMonth = findViewById(R.id.dateThisMonth);
        dateCustomRange = findViewById(R.id.dateCustomRange);

        // Custom range options
        customRangeOptions = findViewById(R.id.customRangeOptions);
        fromDateButton = findViewById(R.id.fromDateButton);
        toDateButton = findViewById(R.id.toDateButton);

        // Diaper filter options
        diaperPoopOnly = findViewById(R.id.diaperPoopOnly);
        diaperPeeOnly = findViewById(R.id.diaperPeeOnly);
        diaperAny = findViewById(R.id.diaperAny);

        // Formula filter options
        formulaAny = findViewById(R.id.formulaAny);
        formulaLess100 = findViewById(R.id.formulaLess100);
        formulaMore200 = findViewById(R.id.formulaMore200);

        // Custom formula amount options
        customAmountOptions = findViewById(R.id.customAmountOptions);
        customAmountInput = findViewById(R.id.customAmountInput);

        // Action button
        applyFiltersButton = findViewById(R.id.applyFiltersButton);
    }

    /**
     * Set up click listeners for all interactive elements
     */
    private void setupClickListeners() {
        // Back button
        backButton.setOnClickListener(v -> finish());

        // Date filter expand/collapse
        dateFilterHeader.setOnClickListener(v -> toggleDateFilter());

        // Diaper filter expand/collapse
        diaperFilterHeader.setOnClickListener(v -> toggleDiaperFilter());

        // Formula filter expand/collapse
        formulaFilterHeader.setOnClickListener(v -> toggleFormulaFilter());

        // Date filter options
        dateToday.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_TODAY));
        dateThisWeek.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_THIS_WEEK));
        dateThisMonth.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_THIS_MONTH));
        dateCustomRange.setOnClickListener(v -> selectDateFilter(FilterCriteria.DATE_CUSTOM_RANGE));

        // Custom range date pickers
        fromDateButton.setOnClickListener(v -> showFromDatePicker());
        toDateButton.setOnClickListener(v -> showToDatePicker());

        // Diaper filter options
        diaperPoopOnly.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_POOP_ONLY));
        diaperPeeOnly.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_PEE_ONLY));
        diaperAny.setOnClickListener(v -> selectDiaperFilter(FilterCriteria.DIAPER_ANY));

        // Formula filter options
        formulaAny.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_ANY));
        formulaLess100.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_LESS_100));
        formulaMore200.setOnClickListener(v -> selectFormulaFilter(FilterCriteria.FORMULA_MORE_200));

        // Action button
        applyFiltersButton.setOnClickListener(v -> applyFilters());
    }

    /**
     * Toggle Date filter section expand/collapse
     */
    private void toggleDateFilter() {
        if (isDateExpanded) {
            // Collapse
            dateFilterContent.setVisibility(View.GONE);
            dateExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isDateExpanded = false;
        } else {
            // Expand
            dateFilterContent.setVisibility(View.VISIBLE);
            dateExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isDateExpanded = true;
        }
    }

    /**
     * Toggle Diaper filter section expand/collapse
     */
    private void toggleDiaperFilter() {
        if (isDiaperExpanded) {
            // Collapse
            diaperFilterContent.setVisibility(View.GONE);
            diaperExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isDiaperExpanded = false;
        } else {
            // Expand
            diaperFilterContent.setVisibility(View.VISIBLE);
            diaperExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isDiaperExpanded = true;
        }
    }

    /**
     * Toggle Formula filter section expand/collapse
     */
    private void toggleFormulaFilter() {
        if (isFormulaExpanded) {
            // Collapse
            formulaFilterContent.setVisibility(View.GONE);
            formulaExpandIcon.setImageResource(R.drawable.ic_expand_more);
            isFormulaExpanded = false;
        } else {
            // Expand
            formulaFilterContent.setVisibility(View.VISIBLE);
            formulaExpandIcon.setImageResource(R.drawable.ic_expand_less);
            isFormulaExpanded = true;
        }
    }

    /**
     * Select date filter option
     */
    private void selectDateFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedDateFilter())) {
            filterCriteria.setSelectedDateFilter(null);
            customRangeOptions.setVisibility(View.GONE);
        } else {
            filterCriteria.setSelectedDateFilter(filter);
            // Show custom range options if custom range is selected
            if (FilterCriteria.DATE_CUSTOM_RANGE.equals(filter)) {
                customRangeOptions.setVisibility(View.VISIBLE);
                initializeCustomRangeDates();
            } else {
                customRangeOptions.setVisibility(View.GONE);
            }
        }
        updateDateFilterUI();
    }

    /**
     * Select diaper filter option
     */
    private void selectDiaperFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedDiaperFilter())) {
            filterCriteria.setSelectedDiaperFilter(null);
        } else {
            filterCriteria.setSelectedDiaperFilter(filter);
        }
        updateDiaperFilterUI();
    }

    /**
     * Select formula filter option
     */
    private void selectFormulaFilter(String filter) {
        // Toggle filter - if same filter is selected, deselect it
        if (filter.equals(filterCriteria.getSelectedFormulaFilter())) {
            filterCriteria.setSelectedFormulaFilter(null);
            customAmountOptions.setVisibility(View.GONE);
        } else {
            filterCriteria.setSelectedFormulaFilter(filter);
            // Show custom amount input if "Any Amount" is selected
            if (FilterCriteria.FORMULA_ANY.equals(filter)) {
                customAmountOptions.setVisibility(View.VISIBLE);
                // Load existing custom amount if available
                if (filterCriteria.getCustomFormulaAmount() != null) {
                    customAmountInput.setText(String.valueOf(filterCriteria.getCustomFormulaAmount()));
                }
            } else {
                customAmountOptions.setVisibility(View.GONE);
            }
        }
        updateFormulaFilterUI();
    }

    /**
     * Initialize date restrictions based on entry date range
     */
    private void initializeDateRestrictions() {
        // Get date range from MainActivity (first to last entry)
        Intent intent = getIntent();
        if (intent.hasExtra("first_entry_date") && intent.hasExtra("last_entry_date")) {
            long firstDate = intent.getLongExtra("first_entry_date", System.currentTimeMillis());
            long lastDate = intent.getLongExtra("last_entry_date", System.currentTimeMillis());

            // Set allowed date range for restrictions
            minAllowedDate = new Date(firstDate);
            maxAllowedDate = new Date(lastDate);
        } else {
            // Fallback to current date
            Date currentDate = new Date();
            minAllowedDate = currentDate;
            maxAllowedDate = currentDate;
        }
    }

    /**
     * Initialize custom range dates to first and last entry dates
     */
    private void initializeCustomRangeDates() {
        // Ensure date restrictions are set
        if (minAllowedDate == null || maxAllowedDate == null) {
            initializeDateRestrictions();
        }

        // Initialize selected dates to the full range
        selectedFromDate = new Date(minAllowedDate.getTime());
        selectedToDate = new Date(maxAllowedDate.getTime());

        updateDateButtonTexts();
    }

    /**
     * Show date picker for "From" date with restrictions
     */
    private void showFromDatePicker() {
        Calendar calendar = Calendar.getInstance();
        if (selectedFromDate != null) {
            calendar.setTime(selectedFromDate);
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                Calendar selectedCalendar = Calendar.getInstance();
                selectedCalendar.set(year, month, dayOfMonth);
                selectedFromDate = selectedCalendar.getTime();
                updateDateButtonTexts();
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );

        // Set date restrictions - cannot select before first entry date
        if (minAllowedDate != null) {
            datePickerDialog.getDatePicker().setMinDate(minAllowedDate.getTime());
        }
        if (maxAllowedDate != null) {
            datePickerDialog.getDatePicker().setMaxDate(maxAllowedDate.getTime());
        }

        datePickerDialog.show();
    }

    /**
     * Show date picker for "To" date with restrictions
     */
    private void showToDatePicker() {
        Calendar calendar = Calendar.getInstance();
        if (selectedToDate != null) {
            calendar.setTime(selectedToDate);
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                Calendar selectedCalendar = Calendar.getInstance();
                selectedCalendar.set(year, month, dayOfMonth);
                selectedToDate = selectedCalendar.getTime();
                updateDateButtonTexts();
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );

        // Set date restrictions - cannot select before first entry date or after last entry date
        if (minAllowedDate != null) {
            datePickerDialog.getDatePicker().setMinDate(minAllowedDate.getTime());
        }
        if (maxAllowedDate != null) {
            datePickerDialog.getDatePicker().setMaxDate(maxAllowedDate.getTime());
        }

        datePickerDialog.show();
    }

    /**
     * Update date button texts with selected dates
     */
    private void updateDateButtonTexts() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());

        if (selectedFromDate != null) {
            fromDateButton.setText(dateFormat.format(selectedFromDate));
        }

        if (selectedToDate != null) {
            toDateButton.setText(dateFormat.format(selectedToDate));
        }
    }

    /**
     * Apply selected filters and return to main activity
     */
    private void applyFilters() {
        // If custom range is selected, save the selected dates
        if (FilterCriteria.DATE_CUSTOM_RANGE.equals(filterCriteria.getSelectedDateFilter())) {
            filterCriteria.setCustomFromDate(selectedFromDate);
            filterCriteria.setCustomToDate(selectedToDate);
            filterCriteria.setIsDateRange(true); // Always use date range (no exact date option)
        }

        // If "Any Amount" formula filter is selected, save the custom amount
        if (FilterCriteria.FORMULA_ANY.equals(filterCriteria.getSelectedFormulaFilter())) {
            String amountText = customAmountInput.getText().toString().trim();
            if (!amountText.isEmpty()) {
                try {
                    int customAmount = Integer.parseInt(amountText);
                    filterCriteria.setCustomFormulaAmount(customAmount);
                } catch (NumberFormatException e) {
                    filterCriteria.setCustomFormulaAmount(null);
                }
            } else {
                filterCriteria.setCustomFormulaAmount(null);
            }
        }

        // Always return filter criteria, even if empty (to allow clearing filters)
        Intent resultIntent = new Intent();
        resultIntent.putExtra("filter_criteria", filterCriteria);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    /**
     * Update UI to show currently selected filters
     */
    private void updateFilterSelectionUI() {
        // Always initialize date restrictions first (needed for calendar pickers)
        initializeDateRestrictions();

        // Update date filter options
        updateDateFilterUI();
        updateDiaperFilterUI();
        updateFormulaFilterUI();

        // If custom range is already selected, show the custom range options
        if (FilterCriteria.DATE_CUSTOM_RANGE.equals(filterCriteria.getSelectedDateFilter())) {
            customRangeOptions.setVisibility(View.VISIBLE);
            // Load existing custom dates if available
            if (filterCriteria.getCustomFromDate() != null && filterCriteria.getCustomToDate() != null) {
                selectedFromDate = filterCriteria.getCustomFromDate();
                selectedToDate = filterCriteria.getCustomToDate();
                updateDateButtonTexts();
            } else {
                // Initialize with default range if no custom dates set
                initializeCustomRangeDates();
            }
        }
    }

    /**
     * Update date filter UI to show current selection
     */
    private void updateDateFilterUI() {
        // Reset all date options
        resetTextViewStyle(dateToday);
        resetTextViewStyle(dateThisWeek);
        resetTextViewStyle(dateThisMonth);
        resetTextViewStyle(dateCustomRange);

        // Highlight selected option
        String selectedDate = filterCriteria.getSelectedDateFilter();
        if (selectedDate != null) {
            switch (selectedDate) {
                case FilterCriteria.DATE_TODAY:
                    highlightTextView(dateToday);
                    break;
                case FilterCriteria.DATE_THIS_WEEK:
                    highlightTextView(dateThisWeek);
                    break;
                case FilterCriteria.DATE_THIS_MONTH:
                    highlightTextView(dateThisMonth);
                    break;
                case FilterCriteria.DATE_CUSTOM_RANGE:
                    highlightTextView(dateCustomRange);
                    break;
            }
        }
    }

    /**
     * Update diaper filter UI to show current selection
     */
    private void updateDiaperFilterUI() {
        // Reset all diaper options
        resetTextViewStyle(diaperPoopOnly);
        resetTextViewStyle(diaperPeeOnly);
        resetTextViewStyle(diaperAny);

        // Highlight selected option
        String selectedDiaper = filterCriteria.getSelectedDiaperFilter();
        if (selectedDiaper != null) {
            switch (selectedDiaper) {
                case FilterCriteria.DIAPER_POOP_ONLY:
                    highlightTextView(diaperPoopOnly);
                    break;
                case FilterCriteria.DIAPER_PEE_ONLY:
                    highlightTextView(diaperPeeOnly);
                    break;
                case FilterCriteria.DIAPER_ANY:
                    highlightTextView(diaperAny);
                    break;
            }
        }
    }

    /**
     * Update formula filter UI to show current selection
     */
    private void updateFormulaFilterUI() {
        // Reset all formula options
        resetTextViewStyle(formulaAny);
        resetTextViewStyle(formulaLess100);
        resetTextViewStyle(formulaMore200);

        // Highlight selected option
        String selectedFormula = filterCriteria.getSelectedFormulaFilter();
        if (selectedFormula != null) {
            switch (selectedFormula) {
                case FilterCriteria.FORMULA_ANY:
                    highlightTextView(formulaAny);
                    // Show custom amount input if "Any Amount" is selected
                    customAmountOptions.setVisibility(View.VISIBLE);
                    if (filterCriteria.getCustomFormulaAmount() != null) {
                        customAmountInput.setText(String.valueOf(filterCriteria.getCustomFormulaAmount()));
                    }
                    break;
                case FilterCriteria.FORMULA_LESS_100:
                    highlightTextView(formulaLess100);
                    break;
                case FilterCriteria.FORMULA_MORE_200:
                    highlightTextView(formulaMore200);
                    break;
            }
        }
    }

    /**
     * Highlight a TextView to show it's selected
     */
    private void highlightTextView(TextView textView) {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                textView.setBackgroundColor(getResources().getColor(R.color.baby_pink, null));
                textView.setTextColor(getResources().getColor(R.color.white, null));
            } else {
                textView.setBackgroundColor(getResources().getColor(R.color.baby_pink));
                textView.setTextColor(getResources().getColor(R.color.white));
            }
        } catch (Exception e) {
            // Fallback to simple highlighting
            textView.setBackgroundColor(0xFFE91E63); // Pink color
            textView.setTextColor(0xFFFFFFFF); // White color
        }
    }

    /**
     * Reset TextView style to default
     */
    private void resetTextViewStyle(TextView textView) {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                textView.setBackground(getResources().getDrawable(android.R.attr.selectableItemBackground, null));
                textView.setTextColor(getResources().getColor(R.color.dark_blue, null));
            } else {
                textView.setBackgroundResource(android.R.attr.selectableItemBackground);
                textView.setTextColor(getResources().getColor(R.color.dark_blue));
            }
        } catch (Exception e) {
            // Fallback to simple reset
            textView.setBackgroundColor(0x00000000); // Transparent
            textView.setTextColor(0xFF1976D2); // Dark blue color
        }
    }
}
