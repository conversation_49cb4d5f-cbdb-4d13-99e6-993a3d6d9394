<resources>
    <string name="app_name">Baby App</string>
    <string name="click_me">Click Me!</string>
    <string name="calendar_icon_description">Calendar picker</string>
    <string name="date_selected">Date selected: %1$s</string>

    <!-- Settings strings -->
    <string name="settings_icon_description">Settings</string>
    <string name="settings_title">Settings</string>
    <string name="first_name_label">First Name</string>
    <string name="first_name_hint">Enter your first name</string>
    <string name="last_name_label">Last Name</string>
    <string name="last_name_hint">Enter your last name</string>
    <string name="password_label">Password</string>
    <string name="password_hint">Enter your password</string>
    <string name="email_label">Email</string>
    <string name="email_hint">Enter your email address</string>
    <string name="time_format_label">Time Format</string>
    <string name="time_format_switch_label_12h">12-hour format (3:30 PM)</string>
    <string name="time_format_switch_label_24h">24-hour format (15:30)</string>
    <string name="time_format_example_12h">Currently using: 12-hour format (3:30 PM)</string>
    <string name="time_format_example_24h">Currently using: 24-hour format (15:30)</string>
    <string name="language_label">Language</string>
    <string name="back_button">Back</string>
    <string name="save_button">Save</string>

    <!-- Validation messages -->
    <string name="required_fields_message">Please fill in all required fields</string>
    <string name="invalid_email_message">Please enter a valid email address</string>
    <string name="password_too_short">Password must be at least 6 characters long</string>

    <!-- Language options array -->
    <string-array name="language_options">
        <item>English</item>
        <item>Serbian</item>
    </string-array>

    <!-- Main Activity strings -->
    <string name="main_title">Baby Care</string>
    <string name="add_entry">Add Entry</string>
    <string name="filter_entries">Filter</string>
    <string name="sort_entries">Sort</string>

    <!-- Multi-Select strings -->
    <string name="select_all_text">Select all</string>
    <string name="delete_all_button">Delete all</string>
    <string name="delete_selected_title">Delete Selected Entries</string>
    <string name="delete_selected_message">Are you sure you want to delete %d selected entries? This action cannot be undone.</string>
    <string name="delete_all_title">Delete All Entries</string>
    <string name="delete_all_message">Are you sure you want to delete all %d entries? This action cannot be undone.</string>
    <string name="confirm_button">Confirm</string>
    <string name="cancel_button">Cancel</string>
    <string name="entries_deleted_success">%d entries deleted successfully</string>
    <string name="selected_count">%d of %d selected</string>

    <!-- Sort Bottom Sheet -->
    <string name="sort_by_title">Sort By:</string>
    <string name="sort_by_date">Date</string>
    <string name="sort_by_time">Time</string>
    <string name="no_entries_message">No entries added yet</string>
    <string name="entries_count">Showing %d entries</string>

    <!-- Main Activity UI elements -->
    <string name="notifications_icon_description">Notifications</string>
    <string name="sort_icon_description">Sort entries</string>
    <string name="filter_icon_description">Filter entries</string>
    <string name="baby_icon_description">Baby icon</string>
    <string name="showing_entries_default">Showing 0 entries</string>
    <string name="add_button">Add</string>
    <string name="active_filters_label">Active Filters:</string>
    <string name="welcome_message">Welcome to your baby\'s journal!</string>
    <string name="welcome_description">Start tracking your little one\'s daily activities.\nTap \'Add\' to create your first entry.</string>
    <string name="today_date">Today</string>

    <!-- Add Entry Activity strings -->
    <string name="add_entry_title">Add New Entry</string>
    <string name="time_section">⏰ Time</string>
    <string name="date_label">Date:</string>
    <string name="time_label">Time:</string>
    <string name="hour_label">h</string>
    <string name="minute_label">m</string>
    <string name="save_entry_button">Save Entry</string>

    <!-- Date/Time hints -->
    <string name="day_hint">15</string>
    <string name="month_hint">12</string>
    <string name="year_hint">2024</string>
    <string name="hour_hint">14</string>
    <string name="minute_hint">30</string>

    <!-- Formula section -->
    <string name="formula_feeding_label">Formula feeding</string>
    <string name="ml_unit">ml</string>
    <string name="formula_amount_hint">150</string>

    <!-- Nursing section -->
    <string name="nursing_duration_hint">10</string>
    <string name="min_unit">min</string>

    <!-- Notifications section -->
    <string name="notification_description">Notify if baby nurses for more than 3 hours</string>

    <!-- Description section -->
    <string name="description_section">📝 Description</string>
    <string name="description_hint_add_entry">Enter description...</string>

    <!-- Dialog strings -->
    <string name="delete_entry_title">Delete Entry?</string>
    <string name="delete_entry_message">Are you sure you want to delete this baby care entry? This action cannot be undone.</string>
    <string name="ok_button">OK</string>

    <!-- Diaper section -->
    <string name="diaper_section">👶 Diaper</string>
    <string name="poop_label">Poop</string>
    <string name="pee_label">Pee</string>

    <!-- Formula section -->
    <string name="formula_section">🍼 Formula</string>
    <string name="formula_amount_label">Amount (ml)</string>

    <!-- Nursing section -->
    <string name="nursing_section">🤱 Nursing</string>
    <string name="left_breast_label">Left Breast</string>
    <string name="right_breast_label">Right Breast</string>
    <string name="nursing_duration_label">Duration (min)</string>

    <!-- Burping section -->
    <string name="burping_section">🤱 Burping</string>
    <string name="burping_label">Baby burped after feeding</string>

    <!-- Notifications section -->
    <string name="notifications_section">🔔 Notifications</string>
    <string name="enable_notifications_label">Enable reminder</string>

    <!-- Description section (using existing description_section from above) -->

    <!-- PDF Export -->
    <string name="pdf_export_title">📄 Export PDF Table</string>
    <string name="pdf_columns_title">Select Columns:</string>
    <string name="pdf_time_column">⏰ Time</string>
    <string name="pdf_poop_column">💩 Poop</string>
    <string name="pdf_pee_column">💧 Pee</string>
    <string name="pdf_formula_column">🍼 Formula</string>
    <string name="pdf_nursing_column">🤱 Nursing</string>
    <string name="pdf_burping_column">🤱 Burping</string>
    <string name="pdf_notes_column">📝 Notes</string>
    <string name="pdf_rows_label">Number of Rows:</string>
    <string name="pdf_rows_hint">Enter number (e.g., 10)</string>
    <string name="pdf_orientation_label">Page Orientation:</string>
    <string name="pdf_portrait">📄 Portrait</string>
    <string name="pdf_landscape">📄 Landscape</string>
    <string name="pdf_include_checkboxes">📋 Include checkboxes in PDF</string>
    <string name="pdf_generate_button">Generate PDF</string>

    <!-- PDF Export Error Messages -->
    <string name="pdf_error_enter_rows">Please enter number of rows</string>
    <string name="pdf_error_positive_number">Please enter a number greater than 0</string>
    <string name="pdf_error_valid_number">Please enter a valid number</string>
    <string name="pdf_error_select_column">Please select at least one additional column</string>
    <string name="pdf_error_max_rows_portrait">Maximum %d rows allowed for Portrait orientation</string>
    <string name="pdf_error_max_rows_landscape">Maximum %d rows allowed for Landscape orientation</string>
    <string name="pdf_error_preparing">Error preparing PDF export</string>

    <!-- Success/Error messages -->
    <string name="pdf_generating">Generating PDF...</string>
    <string name="pdf_generation_error">Error generating PDF</string>
    <string name="entry_saved_success">Entry saved successfully</string>
    <string name="entry_deleted_success">Entry deleted</string>

    <!-- Filter Activity -->
    <string name="filter_title">Filter Entries</string>
    <string name="filter_date_section">Date</string>
    <string name="filter_diaper_section">Diaper</string>
    <string name="filter_formula_section">Formula</string>
    <string name="apply_filters_button">Apply Filters</string>

    <!-- Filter options -->
    <string name="filter_by_date_range">Filter by date range</string>
    <string name="filter_today">Today</string>
    <string name="filter_this_week">This Week</string>
    <string name="filter_this_month">This Month</string>
    <string name="filter_custom_range">Custom Range</string>
    <string name="filter_from_date">From</string>
    <string name="filter_to_date">To</string>
    <string name="filter_select_date">Select Date</string>
    <string name="filter_custom_range_display">%1$s - %2$s</string>

    <!-- Diaper filter options -->
    <string name="filter_by_diaper_type">Filter by diaper type</string>
    <string name="filter_poop_only">Poop Only</string>
    <string name="filter_pee_only">Pee Only</string>
    <string name="filter_any_diaper">Any Diaper Change</string>

    <!-- Formula filter options -->
    <string name="filter_by_formula_amount">Filter by formula amount</string>
    <string name="filter_any_amount">Any Amount</string>
    <string name="filter_less_100ml">Less than 100ml</string>
    <string name="filter_more_200ml">More than 200ml</string>
    <string name="filter_custom_amount_label">Custom:</string>
    <string name="filter_custom_amount_hint">150</string>

    <!-- Notification strings -->
    <string name="notification_title">Baby Care Reminder</string>
    <string name="notification_message">Time for next activity</string>

    <!-- NotificationList Activity -->
    <string name="notifications_title">Notifications</string>
    <string name="next_reminder">Next reminder: %s</string>
    <string name="notification_status_label">Notification Status</string>
    <string name="status_loading">Status: Loading...</string>
    <string name="test_notification_button">Test Notification</string>
    <string name="settings_button">Settings</string>
    <string name="no_notifications_icon">No notifications</string>
    <string name="no_active_notifications">No Active Notifications</string>
    <string name="no_notifications_description">You don\'t have any scheduled notifications.\nEnable notifications when adding nursing entries to get feeding reminders.</string>
    <string name="notification_tip">💡 Tip: Use the \'Test Notification\' button above to see how notifications work!</string>
    <string name="notification_settings_title">Notification Settings</string>

    <!-- PDF Column Headers -->
    <string name="pdf_column_time">Time</string>
    <string name="pdf_column_poop">Poop</string>
    <string name="pdf_column_pee">Pee</string>
    <string name="pdf_column_formula">Formula</string>
    <string name="pdf_column_nursing">Nursing</string>
    <string name="pdf_column_burping">Burping</string>
    <string name="pdf_column_notes">Notes</string>

    <!-- MainActivity Dialog Messages -->
    <string name="scheduled_notification_title">Scheduled Notification</string>
    <string name="missed_notification_default">You had scheduled notifications while you were away.</string>
    <string name="missed_notification_single">You had 1 scheduled notification while you were away.</string>
    <string name="missed_notification_multiple">You had %d scheduled notifications while you were away.</string>

    <!-- AddEntryActivity Messages -->
    <string name="entry_saved_success_simple">Entry saved successfully!</string>
    <string name="notification_scheduled_for">Notification scheduled for %s.</string>
    <string name="generating_pdf">Generating PDF...</string>
    <string name="pdf_generated_success">PDF generated successfully!\n\nFile saved to:\n%s</string>
    <string name="pdf_generation_failed">Failed to generate PDF. Please try again.</string>
    <string name="pdf_generation_error_with_message">Error generating PDF: %s</string>

    <!-- Time formatting -->
    <string name="minutes_short">m</string>
    <string name="hours_short">h</string>
    <string name="hour_singular">1 hour</string>
    <string name="hours_plural">%d hours</string>
    <string name="minutes_plural">%d minutes</string>
    <string name="hours_minutes_format" formatted="false">%dh %dm</string>

    <!-- Activity Item Buttons -->
    <string name="edit_button">Edit</string>
    <string name="delete_button">Delete</string>
    <string name="delete_entry_icon">Delete entry</string>
    <string name="no_activities_recorded">No activities recorded</string>

    <!-- Activity Summary Labels -->
    <string name="activity_diaper_label">Diaper:</string>
    <string name="activity_formula_label">Formula:</string>
    <string name="activity_nursing_label">Nursing:</string>
    <string name="activity_burping_label">Burping:</string>
    <string name="activity_no_description">No description</string>

    <!-- Filter Chip Labels (for display on main screen) -->
    <string name="filter_chip_today">Today</string>
    <string name="filter_chip_this_week">This Week</string>
    <string name="filter_chip_this_month">This Month</string>
    <string name="filter_chip_custom_range">Custom Range</string>
    <string name="filter_chip_poop_only">Poop Only</string>
    <string name="filter_chip_pee_only">Pee Only</string>
    <string name="filter_chip_any_diaper">Any Diaper Change</string>
    <string name="filter_chip_any_amount">Any Amount</string>
    <string name="filter_chip_less_100ml">Less than 100ml</string>
    <string name="filter_chip_more_200ml">More than 200ml</string>
    <string name="filter_chip_custom_amount">%d ml</string>

    <!-- Notification Cancellation Dialog -->
    <string name="cancel_notification_title">Cancel Notification?</string>
    <string name="cancel_notification_message">Are you sure you want to cancel this baby care reminder? You won\'t receive this notification.</string>

    <!-- Notification Settings Activity -->
    <string name="notification_settings_description">Set the time interval for nursing notifications. You will be notified if the baby nurses for longer than the selected time.</string>
    <string name="notification_interval_label">Notification Interval</string>
    <string name="notification_feedback_label">Notification Feedback</string>

    <!-- Time Interval Options -->
    <string name="time_1_hour">1 hour</string>
    <string name="time_2_hours">2 hours</string>
    <string name="time_3_hours">3 hours</string>
    <string name="time_4_hours">4 hours</string>
    <string name="time_6_hours">6 hours</string>
    <string name="time_custom">Custom</string>

    <!-- Custom Time Labels -->
    <string name="custom_time_label">Custom time:</string>
    <string name="hours_label">hours</string>
    <string name="minutes_label">minutes</string>
    <string name="time_hint">0</string>

    <!-- Feedback Options -->
    <string name="vibration_label">Vibration</string>
    <string name="vibration_description">Vibrate device when notification appears</string>
    <string name="sound_label">Sound</string>
    <string name="sound_description">Play sound when notification appears</string>
    <string name="feedback_tip">💡 Sound and vibration help ensure you notice notifications even in silent mode or noisy environments</string>

    <!-- Preview Messages -->
    <string name="preview_invalid_time">Please enter a valid time interval</string>
    <string name="preview_minutes">You will be notified if nursing lasts longer than %d minutes</string>
    <string name="preview_1_hour">You will be notified if nursing lasts longer than 1 hour</string>
    <string name="preview_3_hours">You will be notified if nursing lasts longer than 3 hours</string>
    <string name="preview_hours">You will be notified if nursing lasts longer than %d hours</string>
    <string name="preview_1_hour_minutes">You will be notified if nursing lasts longer than 1 hour and %d minutes</string>
    <string name="preview_hours_minutes">You will be notified if nursing lasts longer than %d hours and %d minutes</string>

    <!-- Toast Messages -->
    <string name="toast_invalid_interval">Please enter a valid time interval</string>
    <string name="toast_interval_too_long">Time interval cannot exceed 24 hours</string>
    <string name="toast_settings_saved">Notification settings saved!</string>

    <!-- Baby Care Reminder (Fullscreen Notification) -->
    <string name="reminder_title">Baby Care Reminder</string>
    <string name="reminder_current_time">Current time: %s</string>
    <string name="reminder_current_time_placeholder">Current time: 15:45</string>

    <!-- Reminder Messages -->
    <string name="reminder_minutes">It\'s been %d minutes since the last activity.\n\nTime to check on your baby!</string>
    <string name="reminder_1_hour">It\'s been 1 hour since the last activity.\n\nTime to check on your baby!</string>
    <string name="reminder_hours">It\'s been %d hours since the last activity.\n\nTime to check on your baby!</string>
    <string name="reminder_1_hour_minutes">It\'s been 1 hour and %d minutes since the last activity.\n\nTime to check on your baby!</string>
    <string name="reminder_hours_minutes">It\'s been %d hours and %d minutes since the last activity.\n\nTime to check on your baby!</string>

    <!-- Reminder Buttons -->
    <string name="reminder_add_entry">Add New Entry</string>
    <string name="reminder_snooze">Snooze (15 minutes)</string>
    <string name="reminder_dismiss">Dismiss</string>

    <!-- Notification Status Messages -->
    <string name="status_label">Status: %s</string>
    <string name="status_notifications_disabled">Notifications disabled</string>
    <string name="status_no_upcoming">No upcoming notifications</string>
    <string name="status_next_hours_minutes">Next notification in %dh %dm</string>
    <string name="status_next_minutes">Next notification in %d minutes</string>
    <string name="status_next_minutes_seconds">Next notification in %dm %ds</string>
    <string name="status_next_seconds">Next notification in %d seconds</string>
    <string name="status_error">Error getting status</string>

    <!-- Settings Change Messages -->
    <string name="applying_changes">Applying Changes</string>
    <string name="please_wait">Please wait...</string>
    <string name="all_changes_saved">All changes saved successfully!</string>
    <string name="language_changed_toast">Language changed successfully!</string>
    <string name="time_format_changed_toast">Time format changed successfully!</string>
</resources>
