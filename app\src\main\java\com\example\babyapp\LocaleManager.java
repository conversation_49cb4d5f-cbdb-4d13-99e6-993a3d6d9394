package com.example.babyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;

import java.util.Locale;

/**
 * Centralized locale management for multi-language support
 * Handles language switching, persistence, and context updates
 */
public class LocaleManager {
    
    private static final String TAG = "LocaleManager";
    private static final String PREFS_NAME = "BabyAppSettings";
    private static final String PREF_LANGUAGE = "language";
    private static final String DEFAULT_LANGUAGE = "English";
    
    // Language code mapping
    private static final String LANGUAGE_ENGLISH = "en";
    private static final String LANGUAGE_SERBIAN = "sr";
    
    // No cache - always read from SharedPreferences for reliability
    
    /**
     * Set locale for the application
     * @param context Application context
     * @param languageDisplayName Display name (English/Serbian)
     */
    public static void setLocale(Context context, String languageDisplayName) {
        String languageCode = getLanguageCode(languageDisplayName);
        Log.d(TAG, "Setting locale to: " + languageDisplayName + " (" + languageCode + ")");
        
        // Save preference
        saveLanguagePreference(context, languageDisplayName);

        // Update resources
        updateResources(context, languageCode);
    }
    
    /**
     * Update base context with locale (for attachBaseContext)
     * @param context Base context
     * @param languageDisplayName Display name (English/Serbian)
     * @return Updated context with locale
     */
    public static Context updateBaseContextLocale(Context context, String languageDisplayName) {
        String languageCode = getLanguageCode(languageDisplayName);
        Log.d(TAG, "Updating base context locale to: " + languageCode);
        
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        
        Configuration config = new Configuration();
        config.setLocale(locale);
        
        return context.createConfigurationContext(config);
    }
    
    /**
     * Get saved language preference
     * @param context Application context
     * @return Saved language display name
     */
    public static String getSavedLanguage(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String savedLanguage = prefs.getString(PREF_LANGUAGE, DEFAULT_LANGUAGE);
        Log.d(TAG, "Read language from SharedPrefs: " + savedLanguage + " (no cache)");
        return savedLanguage;
    }
    
    /**
     * Get current locale for the context
     * @param context Application context
     * @return Current locale
     */
    public static Locale getCurrentLocale(Context context) {
        String languageDisplayName = getSavedLanguage(context);
        String languageCode = getLanguageCode(languageDisplayName);
        return new Locale(languageCode);
    }
    
    /**
     * Convert display name to language code
     * @param languageDisplayName Display name (English/Serbian)
     * @return Language code (en/sr)
     */
    public static String getLanguageCode(String languageDisplayName) {
        switch (languageDisplayName) {
            case "Serbian":
                return LANGUAGE_SERBIAN;
            case "English":
            default:
                return LANGUAGE_ENGLISH;
        }
    }
    
    /**
     * Convert language code to display name
     * @param languageCode Language code (en/sr)
     * @return Display name (English/Serbian)
     */
    public static String getLanguageDisplayName(String languageCode) {
        switch (languageCode) {
            case LANGUAGE_SERBIAN:
                return "Serbian";
            case LANGUAGE_ENGLISH:
            default:
                return "English";
        }
    }
    
    /**
     * Save language preference to SharedPreferences
     * @param context Application context
     * @param languageDisplayName Display name to save
     */
    private static void saveLanguagePreference(Context context, String languageDisplayName) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(PREF_LANGUAGE, languageDisplayName);
        editor.commit(); // Use commit() for immediate save instead of apply()

        Log.d(TAG, "Saved language preference: " + languageDisplayName + " (no cache)");
    }
    
    /**
     * Update resources configuration with new locale
     * @param context Application context
     * @param languageCode Language code (en/sr)
     */
    private static void updateResources(Context context, String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration config = new Configuration(resources.getConfiguration());
        config.setLocale(locale);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createConfigurationContext(config);
        }

        // Always update resources for immediate effect
        resources.updateConfiguration(config, resources.getDisplayMetrics());

        Log.d(TAG, "Updated resources configuration for locale: " + languageCode);
    }
    
    /**
     * Check if current language is Serbian
     * @param context Application context
     * @return true if Serbian is selected
     */
    public static boolean isSerbian(Context context) {
        String language = getSavedLanguage(context);
        return "Serbian".equals(language);
    }
    
    /**
     * Check if current language is English
     * @param context Application context
     * @return true if English is selected
     */
    public static boolean isEnglish(Context context) {
        String language = getSavedLanguage(context);
        return "English".equals(language);
    }

    /**
     * Clear cached language (for debugging) - NO-OP since we removed cache
     */
    public static void clearCache() {
        Log.d(TAG, "clearCache() called - no cache to clear (using direct SharedPreferences)");
    }

    /**
     * Force update application context with new locale
     * @param context Application context
     */
    public static void forceUpdateApplicationLocale(Context context) {
        String savedLanguage = getSavedLanguage(context);
        String languageCode = getLanguageCode(savedLanguage);

        Log.d(TAG, "Force updating application locale to: " + savedLanguage + " (" + languageCode + ")");

        // Update application context
        updateResources(context.getApplicationContext(), languageCode);

        // Update current context
        updateResources(context, languageCode);
    }
}
