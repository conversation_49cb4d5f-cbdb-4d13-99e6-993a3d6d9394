<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    android:background="@drawable/filter_chip_background"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="6dp"
    android:paddingEnd="8dp"
    android:paddingBottom="6dp">

    <TextView
        android:id="@+id/filterChipText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        android:text="Today"
        android:textColor="@color/dark_blue"
        android:textSize="12sp"
        android:textStyle="normal" />

    <ImageView
        android:id="@+id/filterChipClose"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:contentDescription="Remove filter"
        android:focusable="true"
        android:padding="2dp"
        android:src="@drawable/ic_close"
        app:tint="@color/dark_blue" />

</LinearLayout>
