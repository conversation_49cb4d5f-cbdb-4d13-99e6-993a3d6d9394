<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_blue"
    tools:context=".MainActivity">

    <!-- Top Bar with Settings, App Title, and Calendar -->
    <LinearLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Settings Icon on the left -->
        <ImageView
            android:id="@+id/settingsIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/settings_icon_description"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_settings"
            app:tint="@color/white" />

        <!-- App Title in the center -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/appTitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/dateText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/today_date"
                android:textColor="@color/white"
                android:textSize="10sp" />

        </LinearLayout>

        <!-- Notifications Icon on the right -->
        <ImageView
            android:id="@+id/notificationsIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/notifications_icon_description"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_notifications"
            app:tint="@color/white" />

    </LinearLayout>

    <!-- Action Buttons Row with Counter -->
    <LinearLayout
        android:id="@+id/actionButtonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/light_blue"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Showing Counter -->
        <TextView
            android:id="@+id/showingCounterText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/showing_entries_default"
            android:textColor="@color/gray"
            android:textSize="14sp"
            android:textStyle="italic"
            android:gravity="start|center_vertical" />

        <!-- Add New Item Button -->
        <Button
            android:id="@+id/addNewItemButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:backgroundTint="@color/baby_pink"
            android:drawableEnd="@drawable/ic_add"
            android:drawablePadding="4dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/add_button"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            app:drawableTint="@color/white" />

        <!-- Sort Button -->
        <ImageView
            android:id="@+id/sortButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/sort_icon_description"
            android:focusable="true"
            android:padding="4dp"
            android:src="@drawable/ic_sort" />

        <!-- Filter Icon -->
        <ImageView
            android:id="@+id/filterIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/filter_icon_description"
            android:focusable="true"
            android:padding="4dp"
            android:src="@drawable/ic_filter" />

    </LinearLayout>

    <!-- Filter Chips Container (Initially Hidden) -->
    <LinearLayout
        android:id="@+id/filterChipsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:text="@string/active_filters_label"
            android:textColor="@color/dark_blue"
            android:textSize="12sp"
            android:textStyle="bold" />

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/filterChipsLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" />

        </HorizontalScrollView>

    </LinearLayout>

    <!-- Divider Spacer -->
    <View
        android:id="@+id/dividerSpacer"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:background="@color/baby_pink" />

    <!-- Main Content Area -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/light_blue">

        <!-- Entries List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/entriesRecyclerView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Empty State (shown when no entries) -->
        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/babyIcon"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:contentDescription="@string/baby_icon_description"
                android:src="@drawable/ic_baby"
                app:tint="@color/baby_pink" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="@string/welcome_message"
                android:textColor="@color/dark_blue"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:text="@string/welcome_description"
                android:textColor="@color/gray"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👶"
                    android:textSize="24sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🍼"
                    android:textSize="24sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💤"
                    android:textSize="24sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="❤️"
                    android:textSize="24sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
