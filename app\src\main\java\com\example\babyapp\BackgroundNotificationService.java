package com.example.babyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.graphics.Color;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

/**
 * Background service for persistent baby care notifications
 * Ensures notifications work even when app is in background or phone is locked
 */
public class BackgroundNotificationService extends Service {

    private static final String TAG = "BackgroundNotificationService";
    private static final String CHANNEL_ID = "baby_care_background";
    private static final String CHANNEL_NAME = "Baby Care Background";
    private static final int FOREGROUND_NOTIFICATION_ID = 2001;  // For service foreground
    private static final int BACKGROUND_NOTIFICATION_ID = 2002;  // For actual baby care notifications
    private static final long MIN_NOTIFICATION_INTERVAL = 30000; // 30 seconds minimum between notifications

    private NotificationManager notificationManager;
    private static long lastNotificationTime = 0;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "BackgroundNotificationService created");

        createNotificationChannel();
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "BackgroundNotificationService started");

        try {
            if (intent != null) {
                String title = intent.getStringExtra("title");
                String message = intent.getStringExtra("message");
                int intervalMinutes = intent.getIntExtra("interval_minutes", 180);
                long entryId = intent.getLongExtra("entry_id", -1);
                boolean showFullScreen = intent.getBooleanExtra("show_full_screen", true);

                Log.d(TAG, "Processing background notification: " + title + ", entryId: " + entryId);

                // Show the actual notification directly without foreground service
                showBackgroundNotification(title, message, intervalMinutes);

                // Also try to show full-screen notification if requested
                if (showFullScreen) {
                    showFullScreenNotification(title, message, intervalMinutes, entryId);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onStartCommand", e);
        } finally {
            // Stop the service immediately after showing notification
            stopSelf();
            Log.d(TAG, "Service stopped after notification display");
        }

        // Return START_NOT_STICKY since we don't need persistence
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "BackgroundNotificationService destroyed");
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Create foreground notification for service
     */
    private Notification createForegroundNotification() {
        try {
            Intent mainIntent = new Intent(this, MainActivity.class);
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);

            PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("Baby Care Service")
                .setContentText("Processing notification...")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setOngoing(true)
                .setAutoCancel(false)
                .setContentIntent(pendingIntent)
                .build();
        } catch (Exception e) {
            Log.e(TAG, "Error creating foreground notification", e);
            // Return minimal notification as fallback
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("Baby Care Service")
                .setContentText("Running...")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
        }
    }

    /**
     * Create notification channel for background notifications
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription("Background baby care notifications that work when app is closed");
            channel.enableLights(true);
            channel.setLightColor(Color.BLUE);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 500, 200, 500});
            channel.setShowBadge(true);
            channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            channel.setBypassDnd(true); // Critical for baby care
            channel.setSound(android.provider.Settings.System.DEFAULT_NOTIFICATION_URI, null);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
                Log.d(TAG, "Background notification channel created");
            }
        }
    }



    /**
     * Show background notification that works when app is closed
     */
    private void showBackgroundNotification(String title, String message, int intervalMinutes) {
        try {
            // Rate limiting to prevent "noisy" notifications
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastNotificationTime < MIN_NOTIFICATION_INTERVAL) {
                Log.w(TAG, "Skipping notification due to rate limiting (too soon after last notification)");
                return;
            }
            lastNotificationTime = currentTime;

            Log.d(TAG, "Showing background notification: " + title);

            // Record that notification was sent for missed notification tracking
            try {
                MissedNotificationTracker tracker = new MissedNotificationTracker(this);
                tracker.recordNotificationSent(System.currentTimeMillis());
            } catch (Exception e) {
                Log.e(TAG, "Error recording notification for missed tracking", e);
                // Continue with notification display even if tracking fails
            }

            // Create intent to open main app
            // Use flags that bring existing app to front instead of creating new instance
            Intent mainIntent = new Intent(this, MainActivity.class);
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            mainIntent.putExtra("opened_from_notification", true); // Flag to clear badge

            PendingIntent mainPendingIntent = null;
            try {
                mainPendingIntent = PendingIntent.getActivity(
                    this, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
            } catch (Exception e) {
                Log.e(TAG, "Error creating main PendingIntent", e);
            }

            // Create intent to open Add Entry directly
            PendingIntent addEntryPendingIntent = null;
            try {
                Intent addEntryIntent = new Intent(this, AddEntryActivity.class);
                // Use flags that work with existing app state
                addEntryIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                addEntryPendingIntent = PendingIntent.getActivity(
                    this, 1, addEntryIntent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
            } catch (Exception e) {
                Log.e(TAG, "Error creating add entry PendingIntent", e);
            }

            // Build high-priority notification
            NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title != null ? title : "Baby Care Reminder")
                .setContentText(message != null ? message : "Time to check on your baby!")
                .setStyle(new NotificationCompat.BigTextStyle().bigText(message != null ? message : "Time to check on your baby!"))
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setCategory(NotificationCompat.CATEGORY_ALARM)
                .setAutoCancel(true)
                .setOngoing(false)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setLights(0xFF0000FF, 1000, 1000)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

            // Add content intent if available
            if (mainPendingIntent != null) {
                builder.setContentIntent(mainPendingIntent);
            }

            // Add action if available
            if (addEntryPendingIntent != null) {
                try {
                    builder.addAction(android.R.drawable.ic_input_add, "Add Entry", addEntryPendingIntent);
                } catch (Exception e) {
                    Log.e(TAG, "Error adding action to notification", e);
                }
            }

            // Show notification with different ID than foreground service
            if (notificationManager != null) {
                try {
                    Notification notification = builder.build();
                    notificationManager.notify(BACKGROUND_NOTIFICATION_ID, notification);
                    Log.d(TAG, "Background notification displayed successfully with ID: " + BACKGROUND_NOTIFICATION_ID);
                } catch (Exception e) {
                    Log.e(TAG, "Error displaying background notification", e);
                }
            } else {
                Log.e(TAG, "NotificationManager is null, cannot display notification");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error showing background notification", e);
        }
    }

    /**
     * Attempt to show full-screen notification
     */
    private void showFullScreenNotification(String title, String message, int intervalMinutes, long entryId) {
        try {
            // Check if we can safely launch the full-screen activity
            if (!canLaunchFullScreenActivity()) {
                Log.w(TAG, "Cannot safely launch full-screen activity, skipping");
                return;
            }

            Intent fullScreenIntent = new Intent(this, FullScreenNotificationActivity.class);
            fullScreenIntent.putExtra("title", title);
            fullScreenIntent.putExtra("message", message);
            fullScreenIntent.putExtra("interval_minutes", intervalMinutes);
            fullScreenIntent.putExtra("entry_id", entryId);

            // Service context also needs FLAG_ACTIVITY_NEW_TASK for safety
            // But we can adjust other flags based on app state
            if (isAppInForeground()) {
                // App is in foreground - use flags that bring to front without clearing stack
                fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                        Intent.FLAG_ACTIVITY_SINGLE_TOP |
                                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                Log.d(TAG, "App in foreground - using reorder flags");
            } else {
                // App is in background - use standard flags for new task
                fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                Log.d(TAG, "App in background - using new task flags");
            }

            startActivity(fullScreenIntent);
            Log.d(TAG, "Full-screen notification launched from background service");

        } catch (Exception e) {
            Log.e(TAG, "Failed to launch full-screen notification from background", e);
        }
    }

    /**
     * Check if the app is currently in foreground
     */
    private boolean isAppInForeground() {
        try {
            android.app.ActivityManager activityManager = (android.app.ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                java.util.List<android.app.ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
                if (runningProcesses != null) {
                    for (android.app.ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                        if (processInfo.processName.equals(getPackageName())) {
                            boolean isInForeground = processInfo.importance == android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND;
                            Log.d(TAG, "App process importance: " + processInfo.importance + " (foreground=" + isInForeground + ")");
                            return isInForeground;
                        }
                    }
                }
            }
            Log.w(TAG, "Could not determine app foreground status, assuming background");
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking app foreground status", e);
            return false;
        }
    }

    /**
     * Check if we can safely launch the full-screen activity
     */
    private boolean canLaunchFullScreenActivity() {
        try {
            // Check if we have permission to show over other apps (Android 6.0+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!android.provider.Settings.canDrawOverlays(this)) {
                    Log.w(TAG, "No permission to draw over other apps");
                    return false;
                }
            }

            // Check if device is in a state where we can show activities
            android.app.ActivityManager activityManager = (android.app.ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                // For newer Android versions, be more conservative
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // Android 10+ has stricter background activity launch restrictions
                    Log.d(TAG, "Android 10+ detected, using conservative approach");
                }
            }

            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error checking if can launch full-screen activity", e);
            return false;
        }
    }
}
