package com.example.babyapp;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * RecyclerView adapter for baby care entries with swipe functionality
 */
public class BabyCareAdapter extends RecyclerView.Adapter<BabyCareAdapter.ViewHolder> {

    private List<BabyCareEntry> entries;
    private OnEntryActionListener listener;
    private Context context;

    // Multi-select functionality
    private boolean isMultiSelectMode = false;
    private Set<Integer> selectedPositions = new HashSet<>();
    private OnMultiSelectListener multiSelectListener;

    /**
     * Interface for handling entry actions
     */
    public interface OnEntryActionListener {
        void onEditEntry(BabyCareEntry entry, int position);
        void onDeleteEntry(BabyCareEntry entry, int position);
    }

    /**
     * Interface for handling multi-select actions
     */
    public interface OnMultiSelectListener {
        void onMultiSelectModeStarted();
        void onMultiSelectModeEnded();
        void onSelectionChanged(int selectedCount);
        void onLongPress(BabyCareEntry entry, int position);
    }

    /**
     * Constructor
     */
    public BabyCareAdapter(List<BabyCareEntry> entries, OnEntryActionListener listener) {
        this.entries = entries;
        this.listener = listener;
    }

    /**
     * Set context for string resources
     */
    public void setContext(Context context) {
        this.context = context;
    }

    /**
     * Set multi-select listener
     */
    public void setMultiSelectListener(OnMultiSelectListener listener) {
        this.multiSelectListener = listener;
    }

    /**
     * Multi-select mode methods
     */
    public boolean isMultiSelectMode() {
        return isMultiSelectMode;
    }

    public void setMultiSelectMode(boolean enabled) {
        this.isMultiSelectMode = enabled;
        if (!enabled) {
            selectedPositions.clear();
        }
        notifyDataSetChanged();
    }

    public void selectAll() {
        selectedPositions.clear();
        for (int i = 0; i < entries.size(); i++) {
            selectedPositions.add(i);
        }
        notifyDataSetChanged();
        if (multiSelectListener != null) {
            multiSelectListener.onSelectionChanged(selectedPositions.size());
        }
    }

    public void clearSelection() {
        selectedPositions.clear();
        notifyDataSetChanged();
        if (multiSelectListener != null) {
            multiSelectListener.onSelectionChanged(0);
        }
    }

    public Set<Integer> getSelectedPositions() {
        return new HashSet<>(selectedPositions);
    }

    public List<BabyCareEntry> getSelectedEntries() {
        List<BabyCareEntry> selectedEntries = new ArrayList<>();
        for (int position : selectedPositions) {
            if (position < entries.size()) {
                selectedEntries.add(entries.get(position));
            }
        }
        return selectedEntries;
    }

    public int getSelectedCount() {
        return selectedPositions.size();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_baby_care_entry, parent, false);
        if (context == null) {
            context = parent.getContext();
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        BabyCareEntry entry = entries.get(position);
        holder.bind(entry, position);
    }

    @Override
    public int getItemCount() {
        return entries.size();
    }

    /**
     * ViewHolder class with swipe functionality and multi-select support
     */
    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView timeText;
        private TextView dateText;
        private TextView activityText;
        private TextView descriptionText;
        private LinearLayout foregroundLayout;
        private LinearLayout backgroundLayout;
        private LinearLayout deleteOverlay;
        private Button editButton;
        private Button deleteButton;
        private CheckBox selectionCheckbox;

        private float initialX;
        private boolean isSwipeInProgress = false;
        private boolean isOverlayVisible = false;
        private static final float SWIPE_THRESHOLD = 200f;
        private static final float OVERLAY_THRESHOLD = 50f;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);

            timeText = itemView.findViewById(R.id.timeText);
            dateText = itemView.findViewById(R.id.dateText);
            activityText = itemView.findViewById(R.id.activityText);
            descriptionText = itemView.findViewById(R.id.descriptionText);
            foregroundLayout = itemView.findViewById(R.id.foregroundLayout);
            backgroundLayout = itemView.findViewById(R.id.backgroundLayout);
            deleteOverlay = itemView.findViewById(R.id.deleteOverlay);
            editButton = itemView.findViewById(R.id.editButton);
            deleteButton = itemView.findViewById(R.id.deleteButton);
            selectionCheckbox = itemView.findViewById(R.id.selectionCheckbox);

            // Error check for deleteOverlay initialization
            if (deleteOverlay == null) {
                android.util.Log.e("BabyCareAdapter", "ViewHolder created - deleteOverlay is NULL! Check layout R.id.deleteOverlay");
            }

            setupSwipeGesture();
        }

        public void bind(BabyCareEntry entry, int position) {
            timeText.setText(entry.getFormattedTime(context));
            dateText.setText(entry.getFormattedDate());

            String activitySummary = entry.getActivitySummary(context);
            if (activitySummary.isEmpty()) {
                activityText.setText(context.getString(R.string.no_activities_recorded));
            } else {
                activityText.setText(activitySummary);
            }

            descriptionText.setText(entry.getShortDescription(context));

            // Multi-select mode handling
            if (isMultiSelectMode) {
                selectionCheckbox.setVisibility(View.VISIBLE);
                selectionCheckbox.setChecked(selectedPositions.contains(position));
                selectionCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    if (isChecked) {
                        selectedPositions.add(position);
                    } else {
                        selectedPositions.remove(position);
                    }
                    if (multiSelectListener != null) {
                        multiSelectListener.onSelectionChanged(selectedPositions.size());
                    }
                });

                // In multi-select mode, clicking item toggles selection
                itemView.setOnClickListener(v -> {
                    if (selectedPositions.contains(position)) {
                        selectedPositions.remove(position);
                        selectionCheckbox.setChecked(false);
                    } else {
                        selectedPositions.add(position);
                        selectionCheckbox.setChecked(true);
                    }
                    if (multiSelectListener != null) {
                        multiSelectListener.onSelectionChanged(selectedPositions.size());
                    }
                });
            } else {
                selectionCheckbox.setVisibility(View.GONE);
                selectionCheckbox.setOnCheckedChangeListener(null);

                // Normal mode - set up long press to start multi-select
                itemView.setOnLongClickListener(v -> {
                    if (multiSelectListener != null) {
                        multiSelectListener.onLongPress(entry, position);
                    }
                    return true;
                });
            }

            // Set up button click listeners
            editButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onEditEntry(entry, position);
                }
                resetSwipe();
            });

            deleteButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteEntry(entry, position);
                }
            });

            // Reset swipe position
            resetSwipe();
        }

        private void setupSwipeGesture() {
            // Use foregroundLayout instead of itemView to avoid CardView interference
            if (foregroundLayout != null) {
                foregroundLayout.setClickable(true);
                foregroundLayout.setFocusable(true);

                // Set up touch listener on foregroundLayout to avoid CardView interference
                foregroundLayout.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            initialX = event.getX();
                            isSwipeInProgress = false;
                            // Reset overlay state without animation to avoid conflicts
                            isOverlayVisible = false;
                            return true;

                        case MotionEvent.ACTION_MOVE:
                            float deltaX = event.getX() - initialX;

                            // Only allow swipe from right to left (negative deltaX) for delete action
                            if (deltaX < -50) { // Swipe left only
                                isSwipeInProgress = true;

                                // Show DELETE overlay when swipe threshold is reached
                                if (deltaX < -OVERLAY_THRESHOLD && !isOverlayVisible) {
                                    showDeleteOverlay();
                                    isOverlayVisible = true;
                                }
                            } else if (deltaX > 50) { // Swipe right - no action
                                isSwipeInProgress = true; // Mark as swipe but don't show overlay
                            }
                            return true;

                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            float finalDeltaX = event.getX() - initialX;

                            // Only trigger delete dialog for left swipe (negative deltaX)
                            if (finalDeltaX < -OVERLAY_THRESHOLD && isOverlayVisible) {
                                // Left swipe threshold reached - trigger delete dialog
                                int position = getAdapterPosition();
                                if (position >= 0 && position < entries.size() && listener != null) {
                                    BabyCareEntry entry = entries.get(position);
                                    listener.onDeleteEntry(entry, position);
                                } else {
                                    android.util.Log.w("BabyCareAdapter", "Cannot trigger dialog - position: " + position + ", entries size: " + entries.size() + ", listener: " + listener);
                                }
                            } else {
                                // Not enough left swipe or right swipe - hide overlay and reset
                                hideDeleteOverlay();
                                isOverlayVisible = false;

                                // Handle click if it wasn't a swipe
                                if (!isSwipeInProgress) {
                                    v.performClick();
                                }
                            }

                            isSwipeInProgress = false;
                            return true;
                    }
                    return false;
                }
            });
            } else {
                android.util.Log.e("BabyCareAdapter", "foregroundLayout is NULL! Cannot set up touch listener");
            }
        }

        private void resetSwipe() {
            // Immediate reset without animation - best for RecyclerView
            isOverlayVisible = false;
            isSwipeInProgress = false;

            if (deleteOverlay != null) {
                deleteOverlay.animate().cancel(); // Stop any running animations
                deleteOverlay.setVisibility(View.GONE);
                deleteOverlay.setAlpha(1f); // Reset for next time
            }
        }

        /**
         * Show DELETE overlay with fade-in animation
         */
        public void showDeleteOverlay() {
            if (deleteOverlay != null) {
                // Cancel any existing animation to prevent conflicts
                deleteOverlay.animate().cancel();

                deleteOverlay.setVisibility(View.VISIBLE);
                deleteOverlay.setAlpha(0f);

                deleteOverlay.animate()
                    .alpha(1f)
                    .setDuration(200)
                    .start();
            } else {
                android.util.Log.e("BabyCareAdapter", "deleteOverlay is NULL! Cannot show overlay");
            }
        }

        /**
         * Hide DELETE overlay with fade-out animation
         */
        public void hideDeleteOverlay() {
            if (deleteOverlay != null) {
                // Reset state immediately for next swipe capability
                resetOverlayState();

                // Cancel any existing animation to prevent conflicts
                deleteOverlay.animate().cancel();

                deleteOverlay.animate()
                    .alpha(0f)
                    .setDuration(200)
                    .withEndAction(() -> {
                        deleteOverlay.setVisibility(View.GONE);
                        deleteOverlay.setAlpha(1f); // Reset for next time
                    })
                    .start();
            } else {
                android.util.Log.e("BabyCareAdapter", "hideDeleteOverlay - deleteOverlay is NULL!");
            }
        }

        /**
         * Reset overlay state flags
         */
        public void resetOverlayState() {
            isOverlayVisible = false;
            isSwipeInProgress = false;
        }
    }

    /**
     * Setup custom swipe-to-delete functionality
     * Note: This method is kept for compatibility but swipe is now handled in ViewHolder
     */
    public void setupSwipeToDelete(RecyclerView recyclerView) {
        // Custom swipe logic is now handled directly in ViewHolder touch listeners
        // This method is kept for compatibility with existing MainActivity code
    }

    /**
     * Add new entry to the list
     */
    public void addEntry(BabyCareEntry entry) {
        entries.add(0, entry); // Add to top of list
        notifyItemInserted(0);
    }

    /**
     * Remove entry from the list
     */
    public void removeEntry(int position) {
        if (position >= 0 && position < entries.size()) {
            entries.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * Update entry in the list
     */
    public void updateEntry(int position, BabyCareEntry entry) {
        if (position >= 0 && position < entries.size()) {
            entries.set(position, entry);
            notifyItemChanged(position);
        }
    }
}
