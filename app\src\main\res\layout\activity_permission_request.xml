<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_pink"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Permission Request Card -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/card_background"
        android:elevation="8dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="32dp">

            <!-- App Icon -->
            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:src="@mipmap/ic_launcher"
                android:contentDescription="Baby Care App Icon" />

            <!-- Title -->
            <TextView
                android:id="@+id/permissionTitleText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:text="Baby Care Notifications"
                android:textColor="@color/dark_blue"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- Description -->
            <TextView
                android:id="@+id/permissionDescriptionText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="Allow notifications to never miss baby care reminders"
                android:textColor="@color/dark_blue"
                android:textSize="16sp" />

            <!-- Permission Benefits -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <!-- Benefit 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:src="@android:drawable/ic_dialog_info"
                        android:contentDescription="Benefit icon" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Never miss feeding times"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Benefit 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:src="@android:drawable/ic_dialog_info"
                        android:contentDescription="Benefit icon" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Works when phone is locked"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Deny Button -->
                <Button
                    android:id="@+id/denyPermissionButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/button_outline"
                    android:text="Deny"
                    android:textColor="@color/dark_blue"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Confirm Button -->
                <Button
                    android:id="@+id/confirmPermissionButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:backgroundTint="@color/dark_blue"
                    android:text="Allow"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
