<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_blue"
    android:orientation="vertical">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/backButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/selectable_background"
            android:clickable="true"
            android:contentDescription="@string/back_button"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_back"
            app:tint="@color/white" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/filter_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Spacer for balance -->
        <View
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </LinearLayout>

    <!-- Filter Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Date Filter Section -->
            <LinearLayout
                android:id="@+id/dateFilterHeader"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@drawable/selectable_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/filter_date_section"
                    android:textColor="@color/dark_blue"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/dateExpandIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_expand_more"
                    app:tint="@color/dark_blue" />

            </LinearLayout>

            <!-- Date Filter Content (Initially Hidden) -->
            <LinearLayout
                android:id="@+id/dateFilterContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="32dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/filter_by_date_range"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />

                <!-- Date range options -->
                <TextView
                    android:id="@+id/dateToday"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_today"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/dateThisWeek"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_this_week"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/dateThisMonth"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_this_month"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/dateCustomRange"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_custom_range"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <!-- Custom Range Options (Initially Hidden) -->
                <LinearLayout
                    android:id="@+id/customRangeOptions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <!-- From Date -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginEnd="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/filter_from_date"
                            android:textColor="@color/gray"
                            android:textSize="12sp"
                            android:layout_marginBottom="4dp" />

                        <Button
                            android:id="@+id/fromDateButton"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@drawable/edit_text_background"
                            android:text="@string/filter_select_date"
                            android:textColor="@color/dark_blue"
                            android:textSize="14sp"
                            android:gravity="center"
                            android:drawableEnd="@drawable/ic_calendar"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                    </LinearLayout>

                    <!-- To Date -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/filter_to_date"
                            android:textColor="@color/gray"
                            android:textSize="12sp"
                            android:layout_marginBottom="4dp" />

                        <Button
                            android:id="@+id/toDateButton"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@drawable/edit_text_background"
                            android:text="@string/filter_select_date"
                            android:textColor="@color/dark_blue"
                            android:textSize="14sp"
                            android:gravity="center"
                            android:drawableEnd="@drawable/ic_calendar"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Diaper Filter Section -->
            <LinearLayout
                android:id="@+id/diaperFilterHeader"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@drawable/selectable_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/filter_diaper_section"
                    android:textColor="@color/dark_blue"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/diaperExpandIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_expand_more"
                    app:tint="@color/dark_blue" />

            </LinearLayout>

            <!-- Diaper Filter Content (Initially Hidden) -->
            <LinearLayout
                android:id="@+id/diaperFilterContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="32dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/filter_by_diaper_type"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />

                <!-- Diaper filter options -->
                <TextView
                    android:id="@+id/diaperPoopOnly"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_poop_only"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/diaperPeeOnly"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_pee_only"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />



                <TextView
                    android:id="@+id/diaperAny"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_any_diaper"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Formula Filter Section -->
            <LinearLayout
                android:id="@+id/formulaFilterHeader"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@drawable/selectable_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/filter_formula_section"
                    android:textColor="@color/dark_blue"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/formulaExpandIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_expand_more"
                    app:tint="@color/dark_blue" />

            </LinearLayout>

            <!-- Formula Filter Content (Initially Hidden) -->
            <LinearLayout
                android:id="@+id/formulaFilterContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="32dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/filter_by_formula_amount"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />

                <!-- Formula filter options -->
                <TextView
                    android:id="@+id/formulaAny"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_any_amount"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <!-- Custom Amount Input (Initially Hidden) -->
                <LinearLayout
                    android:id="@+id/customAmountOptions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/filter_custom_amount_label"
                        android:textColor="@color/gray"
                        android:textSize="12sp"
                        android:layout_marginEnd="8dp" />

                    <EditText
                        android:id="@+id/customAmountInput"
                        android:layout_width="80dp"
                        android:layout_height="40dp"
                        android:background="@drawable/edit_text_background"
                        android:hint="@string/filter_custom_amount_hint"
                        android:textColor="@color/dark_blue"
                        android:textColorHint="@color/gray"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:inputType="number"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ml"
                        android:textColor="@color/gray"
                        android:textSize="14sp"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

                <TextView
                    android:id="@+id/formulaLess100"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_less_100ml"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />



                <TextView
                    android:id="@+id/formulaMore200"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/selectable_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="@string/filter_more_200ml"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Action Button -->
    <Button
        android:id="@+id/applyFiltersButton"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:backgroundTint="@color/baby_pink"
        android:text="@string/apply_filters_button"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>
