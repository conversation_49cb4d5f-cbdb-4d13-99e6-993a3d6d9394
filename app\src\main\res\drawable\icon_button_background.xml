<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/baby_pink">
    <item>
        <layer-list>
            <!-- Inner shadow layer -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="8dp"/>
                    <gradient
                        android:type="radial"
                        android:centerX="0.5"
                        android:centerY="0.5"
                        android:gradientRadius="15dp"
                        android:startColor="#25FF69B4"
                        android:endColor="#00FF69B4"/>
                </shape>
            </item>
            <!-- Main frame layer -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="8dp"/>
                    <stroke android:width="1dp" android:color="@color/baby_pink"/>
                    <solid android:color="@color/white"/>
                </shape>
            </item>
        </layer-list>
    </item>
</ripple>
